name: Publish Python SDK 

on: []

env:
  PYPI_USERNAME: ${{ secrets.PYPI_USERNAME }}
  PYPI_PASSWORD: ${{ secrets.PYPI_PASSWORD }}

jobs:
  build-and-publish:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.x'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install setuptools wheel twine build requests packaging

      - name: Run version check script
        id: version_check_script
        run: |
          VERSION_INCREMENTED=$(python .github/scripts/check_version_has_incremented.py python ./apps/python-sdk/firecrawl firecrawl-py)
          echo "VERSION_INCREMENTED=$VERSION_INCREMENTED" >> $GITHUB_ENV

      - name: Build the package
        if: ${{ env.VERSION_INCREMENTED == 'true' }}
        run: |
          python -m build
        working-directory: ./apps/python-sdk

      - name: Publish to PyPI
        if: ${{ env.VERSION_INCREMENTED == 'true' }}
        env:
          TWINE_USERNAME: ${{ secrets.PYPI_USERNAME }}
          TWINE_PASSWORD: ${{ secrets.PYPI_PASSWORD }}
        run: |
          twine upload dist/*
        working-directory: ./apps/python-sdk

