name: Publish Rust SDK 

on: []

env:
  CRATES_IO_TOKEN: ${{ secrets.CRATES_IO_TOKEN }}

jobs:
  build-and-publish:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
          default: true
          profile: minimal

      - name: Install dependencies
        run: cargo build --release

      - name: Run version check script
        id: version_check_script
        run: |
          VERSION_INCREMENTED=$(cargo search --limit 1 my_crate_name | grep my_crate_name)
          echo "VERSION_INCREMENTED=$VERSION_INCREMENTED" >> $GITHUB_ENV

      - name: Build the package
        if: ${{ env.VERSION_INCREMENTED == 'true' }}
        run: cargo package
        working-directory: ./apps/rust-sdk

      - name: Publish to crates.io
        if: ${{ env.VERSION_INCREMENTED == 'true' }}
        env:
          CARGO_REG_TOKEN: ${{ secrets.CRATES_IO_TOKEN }}
        run: cargo publish
        working-directory: ./apps/rust-sdk