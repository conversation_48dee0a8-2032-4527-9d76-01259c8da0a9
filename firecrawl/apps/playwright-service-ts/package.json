{"name": "playwright-scraper-api", "version": "1.0.0", "description": "scraper api with playwright", "main": "api.ts", "scripts": {"start": "node dist/api.js", "build": "tsc", "dev": "ts-node api.ts"}, "keywords": [], "author": "<PERSON>", "license": "ISC", "dependencies": {"body-parser": "^1.20.2", "dotenv": "^16.4.5", "express": "^4.19.2", "playwright": "^1.45.0", "user-agents": "^1.1.410"}, "devDependencies": {"@types/body-parser": "^1.19.5", "@types/express": "^4.17.21", "@types/node": "^20.14.9", "@types/user-agents": "^1.0.4", "ts-node": "^10.9.2", "typescript": "^5.5.2"}}