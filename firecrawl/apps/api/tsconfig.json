{
  "compilerOptions": {
    "rootDir": "./src",
    "lib": ["ES2022", "DOM"],

    // or higher
    "target": "ES2022",

    "module": "NodeNext",
    "esModuleInterop": true,
    "sourceMap": true,
    "outDir": "./dist/src",
    "moduleResolution": "NodeNext",
    "baseUrl": ".",
    "strictNullChecks": true,

    "inlineSources": true
  },
  "include": [
    "src/",
    "src/**/*",
    "services/db/supabase.ts",
    "utils/utils.ts",
    "services/db/supabaseEmbeddings.ts",
    "utils/EventEmmitter.ts",
    "src/services/queue-service.ts"
  ]
}
