{"name": "test-suite", "version": "1.0.0", "description": "", "scripts": {"test:suite": "npx jest --detectOpenHandles --forceExit --openHandlesTimeout=120000 --watchAll=false", "test:load": "artillery run --output ./load-test-results/test-run-report.json load-test.yml", "test:scrape": "npx jest --detectOpenHandles --forceExit --openHandlesTimeout=120000 --watchAll=false --testPathPattern=tests/scrape.test.ts", "test:crawl": "npx jest --detectOpenHandles --forceExit --openHandlesTimeout=120000 --watchAll=false --testPathPattern=tests/crawl.test.ts", "test:schema-validation": "npx jest --detectOpenHandles --forceExit --openHandlesTimeout=120000 --watchAll=false --testPathPattern=tests/schema-validation.test.ts"}, "author": "", "license": "ISC", "dependencies": {"@anthropic-ai/sdk": "^0.24.3", "@dqbd/tiktoken": "^1.0.14", "@supabase/supabase-js": "^2.43.1", "dotenv": "^16.4.5", "jest": "^29.7.0", "openai": "^4.40.2", "playwright": "^1.43.1", "supertest": "^7.0.0", "ts-jest": "^29.1.2"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/jest": "^29.5.12", "@types/supertest": "^6.0.2", "artillery": "^2.0.19", "typescript": "^5.4.5", "zod": "^3.24.1"}}