{"aggregate": {"counters": {"vusers.created_by_name.Scrape a URL": 9000, "vusers.created": 9000, "http.requests": 9000, "http.codes.200": 8996, "http.responses": 9000, "http.downloaded_bytes": 0, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 8996, "vusers.failed": 4, "vusers.completed": 8996, "http.codes.502": 4, "plugins.metrics-by-endpoint./v0/scrape.codes.502": 4, "errors.Failed capture or match": 4}, "rates": {"http.request_rate": 23}, "firstCounterAt": 1716317566907, "firstHistogramAt": 1716317568357, "lastCounterAt": 1716317987944, "lastHistogramAt": 1716317987944, "firstMetricAt": 1716317566907, "lastMetricAt": 1716317987944, "period": 1716317980000, "summaries": {"http.response_time": {"min": 62, "max": 18924, "count": 9000, "mean": 5661.8, "p50": 5378.9, "median": 5378.9, "p75": 7407.5, "p90": 9801.2, "p95": 11050.8, "p99": 12968.3, "p999": 17158.9}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 62, "max": 18924, "count": 9000, "mean": 5661.8, "p50": 5378.9, "median": 5378.9, "p75": 7407.5, "p90": 9801.2, "p95": 11050.8, "p99": 12968.3, "p999": 17158.9}, "vusers.session_length": {"min": 1079.2, "max": 18980.3, "count": 8996, "mean": 5734.4, "p50": 5487.5, "median": 5487.5, "p75": 7557.1, "p90": 9801.2, "p95": 11050.8, "p99": 12968.3, "p999": 17158.9}}, "histograms": {"http.response_time": {"min": 62, "max": 18924, "count": 9000, "mean": 5661.8, "p50": 5378.9, "median": 5378.9, "p75": 7407.5, "p90": 9801.2, "p95": 11050.8, "p99": 12968.3, "p999": 17158.9}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 62, "max": 18924, "count": 9000, "mean": 5661.8, "p50": 5378.9, "median": 5378.9, "p75": 7407.5, "p90": 9801.2, "p95": 11050.8, "p99": 12968.3, "p999": 17158.9}, "vusers.session_length": {"min": 1079.2, "max": 18980.3, "count": 8996, "mean": 5734.4, "p50": 5487.5, "median": 5487.5, "p75": 7557.1, "p90": 9801.2, "p95": 11050.8, "p99": 12968.3, "p999": 17158.9}}}, "intermediate": [{"counters": {"vusers.created_by_name.Scrape a URL": 32, "vusers.created": 32, "http.requests": 32, "http.codes.200": 14, "http.responses": 14, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 14, "vusers.failed": 0, "vusers.completed": 14}, "rates": {"http.request_rate": 14}, "http.request_rate": null, "firstCounterAt": 1716317566907, "firstHistogramAt": 1716317568357, "lastCounterAt": 1716317569959, "lastHistogramAt": 1716317569748, "firstMetricAt": 1716317566907, "lastMetricAt": 1716317569959, "period": "1716317560000", "summaries": {"http.response_time": {"min": 1246, "max": 1831, "count": 14, "mean": 1465.1, "p50": 1465.9, "median": 1465.9, "p75": 1525.7, "p90": 1652.8, "p95": 1686.1, "p99": 1686.1, "p999": 1686.1}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1246, "max": 1831, "count": 14, "mean": 1465.1, "p50": 1465.9, "median": 1465.9, "p75": 1525.7, "p90": 1652.8, "p95": 1686.1, "p99": 1686.1, "p999": 1686.1}, "vusers.session_length": {"min": 1319.2, "max": 1897.5, "count": 14, "mean": 1537.3, "p50": 1525.7, "median": 1525.7, "p75": 1587.9, "p90": 1720.2, "p95": 1720.2, "p99": 1720.2, "p999": 1720.2}}, "histograms": {"http.response_time": {"min": 1246, "max": 1831, "count": 14, "mean": 1465.1, "p50": 1465.9, "median": 1465.9, "p75": 1525.7, "p90": 1652.8, "p95": 1686.1, "p99": 1686.1, "p999": 1686.1}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1246, "max": 1831, "count": 14, "mean": 1465.1, "p50": 1465.9, "median": 1465.9, "p75": 1525.7, "p90": 1652.8, "p95": 1686.1, "p99": 1686.1, "p999": 1686.1}, "vusers.session_length": {"min": 1319.2, "max": 1897.5, "count": 14, "mean": 1537.3, "p50": 1525.7, "median": 1525.7, "p75": 1587.9, "p90": 1720.2, "p95": 1720.2, "p99": 1720.2, "p999": 1720.2}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 100, "vusers.created": 100, "http.requests": 100, "http.codes.200": 102, "http.responses": 102, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 102, "vusers.failed": 0, "vusers.completed": 102}, "rates": {"http.request_rate": 10}, "http.request_rate": null, "firstCounterAt": 1716317570009, "firstHistogramAt": 1716317570160, "lastCounterAt": 1716317579997, "lastHistogramAt": 1716317579997, "firstMetricAt": 1716317570009, "lastMetricAt": 1716317579997, "period": "1716317570000", "summaries": {"http.response_time": {"min": 1157, "max": 1961, "count": 102, "mean": 1512.3, "p50": 1465.9, "median": 1465.9, "p75": 1652.8, "p90": 1790.4, "p95": 1863.5, "p99": 1901.1, "p999": 1901.1}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1157, "max": 1961, "count": 102, "mean": 1512.3, "p50": 1465.9, "median": 1465.9, "p75": 1652.8, "p90": 1790.4, "p95": 1863.5, "p99": 1901.1, "p999": 1901.1}, "vusers.session_length": {"min": 1215.3, "max": 2020.1, "count": 102, "mean": 1577.1, "p50": 1525.7, "median": 1525.7, "p75": 1686.1, "p90": 1826.6, "p95": 1901.1, "p99": 1939.5, "p999": 1939.5}}, "histograms": {"http.response_time": {"min": 1157, "max": 1961, "count": 102, "mean": 1512.3, "p50": 1465.9, "median": 1465.9, "p75": 1652.8, "p90": 1790.4, "p95": 1863.5, "p99": 1901.1, "p999": 1901.1}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1157, "max": 1961, "count": 102, "mean": 1512.3, "p50": 1465.9, "median": 1465.9, "p75": 1652.8, "p90": 1790.4, "p95": 1863.5, "p99": 1901.1, "p999": 1901.1}, "vusers.session_length": {"min": 1215.3, "max": 2020.1, "count": 102, "mean": 1577.1, "p50": 1525.7, "median": 1525.7, "p75": 1686.1, "p90": 1826.6, "p95": 1901.1, "p99": 1939.5, "p999": 1939.5}}}, {"counters": {"http.codes.200": 99, "http.responses": 99, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 99, "vusers.failed": 0, "vusers.completed": 99, "vusers.created_by_name.Scrape a URL": 100, "vusers.created": 100, "http.requests": 100}, "rates": {"http.request_rate": 10}, "http.request_rate": null, "firstCounterAt": 1716317580009, "firstHistogramAt": 1716317580028, "lastCounterAt": 1716317589959, "lastHistogramAt": 1716317589860, "firstMetricAt": 1716317580009, "lastMetricAt": 1716317589959, "period": "1716317580000", "summaries": {"http.response_time": {"min": 1119, "max": 2155, "count": 99, "mean": 1509.7, "p50": 1495.5, "median": 1495.5, "p75": 1686.1, "p90": 1826.6, "p95": 1901.1, "p99": 2018.7, "p999": 2018.7}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1119, "max": 2155, "count": 99, "mean": 1509.7, "p50": 1495.5, "median": 1495.5, "p75": 1686.1, "p90": 1826.6, "p95": 1901.1, "p99": 2018.7, "p999": 2018.7}, "vusers.session_length": {"min": 1178.2, "max": 2220.5, "count": 99, "mean": 1572.8, "p50": 1556.5, "median": 1556.5, "p75": 1755, "p90": 1863.5, "p95": 1978.7, "p99": 2101.1, "p999": 2101.1}}, "histograms": {"http.response_time": {"min": 1119, "max": 2155, "count": 99, "mean": 1509.7, "p50": 1495.5, "median": 1495.5, "p75": 1686.1, "p90": 1826.6, "p95": 1901.1, "p99": 2018.7, "p999": 2018.7}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1119, "max": 2155, "count": 99, "mean": 1509.7, "p50": 1495.5, "median": 1495.5, "p75": 1686.1, "p90": 1826.6, "p95": 1901.1, "p99": 2018.7, "p999": 2018.7}, "vusers.session_length": {"min": 1178.2, "max": 2220.5, "count": 99, "mean": 1572.8, "p50": 1556.5, "median": 1556.5, "p75": 1755, "p90": 1863.5, "p95": 1978.7, "p99": 2101.1, "p999": 2101.1}}}, {"counters": {"http.codes.200": 103, "http.responses": 103, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 103, "vusers.failed": 0, "vusers.completed": 103, "vusers.created_by_name.Scrape a URL": 100, "vusers.created": 100, "http.requests": 100}, "rates": {"http.request_rate": 10}, "http.request_rate": null, "firstCounterAt": 1716317590008, "firstHistogramAt": 1716317590008, "lastCounterAt": 1716317599981, "lastHistogramAt": 1716317599981, "firstMetricAt": 1716317590008, "lastMetricAt": 1716317599981, "period": "1716317590000", "summaries": {"http.response_time": {"min": 1104, "max": 1962, "count": 103, "mean": 1382.6, "p50": 1380.5, "median": 1380.5, "p75": 1465.9, "p90": 1587.9, "p95": 1652.8, "p99": 1686.1, "p999": 1755}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1104, "max": 1962, "count": 103, "mean": 1382.6, "p50": 1380.5, "median": 1380.5, "p75": 1465.9, "p90": 1587.9, "p95": 1652.8, "p99": 1686.1, "p999": 1755}, "vusers.session_length": {"min": 1165.1, "max": 2035.9, "count": 103, "mean": 1449.5, "p50": 1436.8, "median": 1436.8, "p75": 1525.7, "p90": 1652.8, "p95": 1720.2, "p99": 1755, "p999": 1826.6}}, "histograms": {"http.response_time": {"min": 1104, "max": 1962, "count": 103, "mean": 1382.6, "p50": 1380.5, "median": 1380.5, "p75": 1465.9, "p90": 1587.9, "p95": 1652.8, "p99": 1686.1, "p999": 1755}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1104, "max": 1962, "count": 103, "mean": 1382.6, "p50": 1380.5, "median": 1380.5, "p75": 1465.9, "p90": 1587.9, "p95": 1652.8, "p99": 1686.1, "p999": 1755}, "vusers.session_length": {"min": 1165.1, "max": 2035.9, "count": 103, "mean": 1449.5, "p50": 1436.8, "median": 1436.8, "p75": 1525.7, "p90": 1652.8, "p95": 1720.2, "p99": 1755, "p999": 1826.6}}}, {"counters": {"http.codes.200": 97, "http.responses": 97, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 97, "vusers.failed": 0, "vusers.completed": 97, "vusers.created_by_name.Scrape a URL": 100, "vusers.created": 100, "http.requests": 100}, "rates": {"http.request_rate": 10}, "http.request_rate": null, "firstCounterAt": 1716317600009, "firstHistogramAt": 1716317600103, "lastCounterAt": 1716317609958, "lastHistogramAt": 1716317609766, "firstMetricAt": 1716317600009, "lastMetricAt": 1716317609958, "period": "1716317600000", "summaries": {"http.response_time": {"min": 1091, "max": 1833, "count": 97, "mean": 1405.3, "p50": 1408.4, "median": 1408.4, "p75": 1495.5, "p90": 1652.8, "p95": 1686.1, "p99": 1790.4, "p999": 1790.4}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1091, "max": 1833, "count": 97, "mean": 1405.3, "p50": 1408.4, "median": 1408.4, "p75": 1495.5, "p90": 1652.8, "p95": 1686.1, "p99": 1790.4, "p999": 1790.4}, "vusers.session_length": {"min": 1166.3, "max": 1892.1, "count": 97, "mean": 1470.6, "p50": 1465.9, "median": 1465.9, "p75": 1556.5, "p90": 1686.1, "p95": 1790.4, "p99": 1863.5, "p999": 1863.5}}, "histograms": {"http.response_time": {"min": 1091, "max": 1833, "count": 97, "mean": 1405.3, "p50": 1408.4, "median": 1408.4, "p75": 1495.5, "p90": 1652.8, "p95": 1686.1, "p99": 1790.4, "p999": 1790.4}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1091, "max": 1833, "count": 97, "mean": 1405.3, "p50": 1408.4, "median": 1408.4, "p75": 1495.5, "p90": 1652.8, "p95": 1686.1, "p99": 1790.4, "p999": 1790.4}, "vusers.session_length": {"min": 1166.3, "max": 1892.1, "count": 97, "mean": 1470.6, "p50": 1465.9, "median": 1465.9, "p75": 1556.5, "p90": 1686.1, "p95": 1790.4, "p99": 1863.5, "p999": 1863.5}}}, {"counters": {"http.codes.200": 102, "http.responses": 102, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 102, "vusers.failed": 0, "vusers.completed": 102, "vusers.created_by_name.Scrape a URL": 100, "vusers.created": 100, "http.requests": 100}, "rates": {"http.request_rate": 10}, "http.request_rate": null, "firstCounterAt": 1716317610009, "firstHistogramAt": 1716317610027, "lastCounterAt": 1716317619992, "lastHistogramAt": 1716317619992, "firstMetricAt": 1716317610009, "lastMetricAt": 1716317619992, "period": "1716317610000", "summaries": {"http.response_time": {"min": 1093, "max": 2000, "count": 102, "mean": 1411.5, "p50": 1380.5, "median": 1380.5, "p75": 1495.5, "p90": 1620, "p95": 1686.1, "p99": 1826.6, "p999": 1863.5}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1093, "max": 2000, "count": 102, "mean": 1411.5, "p50": 1380.5, "median": 1380.5, "p75": 1495.5, "p90": 1620, "p95": 1686.1, "p99": 1826.6, "p999": 1863.5}, "vusers.session_length": {"min": 1156.8, "max": 2061.9, "count": 102, "mean": 1477.6, "p50": 1465.9, "median": 1465.9, "p75": 1556.5, "p90": 1686.1, "p95": 1755, "p99": 1901.1, "p999": 1939.5}}, "histograms": {"http.response_time": {"min": 1093, "max": 2000, "count": 102, "mean": 1411.5, "p50": 1380.5, "median": 1380.5, "p75": 1495.5, "p90": 1620, "p95": 1686.1, "p99": 1826.6, "p999": 1863.5}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1093, "max": 2000, "count": 102, "mean": 1411.5, "p50": 1380.5, "median": 1380.5, "p75": 1495.5, "p90": 1620, "p95": 1686.1, "p99": 1826.6, "p999": 1863.5}, "vusers.session_length": {"min": 1156.8, "max": 2061.9, "count": 102, "mean": 1477.6, "p50": 1465.9, "median": 1465.9, "p75": 1556.5, "p90": 1686.1, "p95": 1755, "p99": 1901.1, "p999": 1939.5}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 134, "vusers.created": 134, "http.requests": 134, "http.codes.200": 109, "http.responses": 109, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 109, "vusers.failed": 0, "vusers.completed": 109}, "rates": {"http.request_rate": 13}, "http.request_rate": null, "firstCounterAt": 1716317620009, "firstHistogramAt": 1716317620217, "lastCounterAt": 1716317629997, "lastHistogramAt": 1716317629993, "firstMetricAt": 1716317620009, "lastMetricAt": 1716317629997, "period": "1716317620000", "summaries": {"http.response_time": {"min": 1110, "max": 2261, "count": 109, "mean": 1496, "p50": 1465.9, "median": 1465.9, "p75": 1587.9, "p90": 1826.6, "p95": 1901.1, "p99": 1978.7, "p999": 1978.7}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1110, "max": 2261, "count": 109, "mean": 1496, "p50": 1465.9, "median": 1465.9, "p75": 1587.9, "p90": 1826.6, "p95": 1901.1, "p99": 1978.7, "p999": 1978.7}, "vusers.session_length": {"min": 1171.2, "max": 2329.1, "count": 109, "mean": 1564.2, "p50": 1525.7, "median": 1525.7, "p75": 1652.8, "p90": 1901.1, "p95": 1978.7, "p99": 2018.7, "p999": 2059.5}}, "histograms": {"http.response_time": {"min": 1110, "max": 2261, "count": 109, "mean": 1496, "p50": 1465.9, "median": 1465.9, "p75": 1587.9, "p90": 1826.6, "p95": 1901.1, "p99": 1978.7, "p999": 1978.7}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1110, "max": 2261, "count": 109, "mean": 1496, "p50": 1465.9, "median": 1465.9, "p75": 1587.9, "p90": 1826.6, "p95": 1901.1, "p99": 1978.7, "p999": 1978.7}, "vusers.session_length": {"min": 1171.2, "max": 2329.1, "count": 109, "mean": 1564.2, "p50": 1525.7, "median": 1525.7, "p75": 1652.8, "p90": 1901.1, "p95": 1978.7, "p99": 2018.7, "p999": 2059.5}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 200, "vusers.created": 200, "http.requests": 200, "http.codes.200": 175, "http.responses": 175, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 175, "vusers.failed": 0, "vusers.completed": 175}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1716317630049, "firstHistogramAt": 1716317630049, "lastCounterAt": 1716317639997, "lastHistogramAt": 1716317639994, "firstMetricAt": 1716317630049, "lastMetricAt": 1716317639997, "period": "1716317630000", "summaries": {"http.response_time": {"min": 1788, "max": 4061, "count": 175, "mean": 2507.1, "p50": 2322.1, "median": 2322.1, "p75": 2836.2, "p90": 3395.5, "p95": 3828.5, "p99": 3984.7, "p999": 4065.2}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1788, "max": 4061, "count": 175, "mean": 2507.1, "p50": 2322.1, "median": 2322.1, "p75": 2836.2, "p90": 3395.5, "p95": 3828.5, "p99": 3984.7, "p999": 4065.2}, "vusers.session_length": {"min": 1859.4, "max": 4129.2, "count": 175, "mean": 2576.4, "p50": 2369, "median": 2369, "p75": 2893.5, "p90": 3395.5, "p95": 3984.7, "p99": 4065.2, "p999": 4147.4}}, "histograms": {"http.response_time": {"min": 1788, "max": 4061, "count": 175, "mean": 2507.1, "p50": 2322.1, "median": 2322.1, "p75": 2836.2, "p90": 3395.5, "p95": 3828.5, "p99": 3984.7, "p999": 4065.2}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1788, "max": 4061, "count": 175, "mean": 2507.1, "p50": 2322.1, "median": 2322.1, "p75": 2836.2, "p90": 3395.5, "p95": 3828.5, "p99": 3984.7, "p999": 4065.2}, "vusers.session_length": {"min": 1859.4, "max": 4129.2, "count": 175, "mean": 2576.4, "p50": 2369, "median": 2369, "p75": 2893.5, "p90": 3395.5, "p95": 3984.7, "p99": 4065.2, "p999": 4147.4}}}, {"counters": {"http.codes.200": 190, "http.responses": 190, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 190, "vusers.failed": 0, "vusers.completed": 190, "vusers.created_by_name.Scrape a URL": 200, "vusers.created": 200, "http.requests": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1716317640006, "firstHistogramAt": 1716317640006, "lastCounterAt": 1716317649997, "lastHistogramAt": 1716317649982, "firstMetricAt": 1716317640006, "lastMetricAt": 1716317649997, "period": "1716317640000", "summaries": {"http.response_time": {"min": 1725, "max": 5535, "count": 190, "mean": 3401, "p50": 3328.3, "median": 3328.3, "p75": 4147.4, "p90": 4770.6, "p95": 5065.6, "p99": 5487.5, "p999": 5487.5}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1725, "max": 5535, "count": 190, "mean": 3401, "p50": 3328.3, "median": 3328.3, "p75": 4147.4, "p90": 4770.6, "p95": 5065.6, "p99": 5487.5, "p999": 5487.5}, "vusers.session_length": {"min": 1794.2, "max": 5597.3, "count": 190, "mean": 3472.8, "p50": 3464.1, "median": 3464.1, "p75": 4231.1, "p90": 4770.6, "p95": 5065.6, "p99": 5598.4, "p999": 5598.4}}, "histograms": {"http.response_time": {"min": 1725, "max": 5535, "count": 190, "mean": 3401, "p50": 3328.3, "median": 3328.3, "p75": 4147.4, "p90": 4770.6, "p95": 5065.6, "p99": 5487.5, "p999": 5487.5}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1725, "max": 5535, "count": 190, "mean": 3401, "p50": 3328.3, "median": 3328.3, "p75": 4147.4, "p90": 4770.6, "p95": 5065.6, "p99": 5487.5, "p999": 5487.5}, "vusers.session_length": {"min": 1794.2, "max": 5597.3, "count": 190, "mean": 3472.8, "p50": 3464.1, "median": 3464.1, "p75": 4231.1, "p90": 4770.6, "p95": 5065.6, "p99": 5598.4, "p999": 5598.4}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 200, "vusers.created": 200, "http.requests": 200, "http.codes.200": 193, "http.responses": 193, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 193, "vusers.failed": 0, "vusers.completed": 193}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1716317650017, "firstHistogramAt": 1716317650017, "lastCounterAt": 1716317659997, "lastHistogramAt": 1716317659992, "firstMetricAt": 1716317650017, "lastMetricAt": 1716317659997, "period": "1716317650000", "summaries": {"http.response_time": {"min": 2166, "max": 9876, "count": 193, "mean": 3872.2, "p50": 4065.2, "median": 4065.2, "p75": 4867, "p90": 5168, "p95": 5378.9, "p99": 5944.6, "p999": 6187.2}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 2166, "max": 9876, "count": 193, "mean": 3872.2, "p50": 4065.2, "median": 4065.2, "p75": 4867, "p90": 5168, "p95": 5378.9, "p99": 5944.6, "p999": 6187.2}, "vusers.session_length": {"min": 2231.8, "max": 9951.6, "count": 193, "mean": 3940.3, "p50": 4065.2, "median": 4065.2, "p75": 4867, "p90": 5272.4, "p95": 5487.5, "p99": 6064.7, "p999": 6187.2}}, "histograms": {"http.response_time": {"min": 2166, "max": 9876, "count": 193, "mean": 3872.2, "p50": 4065.2, "median": 4065.2, "p75": 4867, "p90": 5168, "p95": 5378.9, "p99": 5944.6, "p999": 6187.2}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 2166, "max": 9876, "count": 193, "mean": 3872.2, "p50": 4065.2, "median": 4065.2, "p75": 4867, "p90": 5168, "p95": 5378.9, "p99": 5944.6, "p999": 6187.2}, "vusers.session_length": {"min": 2231.8, "max": 9951.6, "count": 193, "mean": 3940.3, "p50": 4065.2, "median": 4065.2, "p75": 4867, "p90": 5272.4, "p95": 5487.5, "p99": 6064.7, "p999": 6187.2}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 200, "vusers.created": 200, "http.requests": 200, "http.codes.200": 178, "http.responses": 178, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 178, "vusers.failed": 0, "vusers.completed": 178}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1716317660016, "firstHistogramAt": 1716317660016, "lastCounterAt": 1716317669997, "lastHistogramAt": 1716317669965, "firstMetricAt": 1716317660016, "lastMetricAt": 1716317669997, "period": "1716317660000", "summaries": {"http.response_time": {"min": 1237, "max": 8511, "count": 178, "mean": 4568.5, "p50": 4770.6, "median": 4770.6, "p75": 6064.7, "p90": 6976.1, "p95": 7407.5, "p99": 8024.5, "p999": 8186.6}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1237, "max": 8511, "count": 178, "mean": 4568.5, "p50": 4770.6, "median": 4770.6, "p75": 6064.7, "p90": 6976.1, "p95": 7407.5, "p99": 8024.5, "p999": 8186.6}, "vusers.session_length": {"min": 1303.1, "max": 8590.5, "count": 178, "mean": 4639, "p50": 4867, "median": 4867, "p75": 6187.2, "p90": 7117, "p95": 7557.1, "p99": 8024.5, "p999": 8352}}, "histograms": {"http.response_time": {"min": 1237, "max": 8511, "count": 178, "mean": 4568.5, "p50": 4770.6, "median": 4770.6, "p75": 6064.7, "p90": 6976.1, "p95": 7407.5, "p99": 8024.5, "p999": 8186.6}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1237, "max": 8511, "count": 178, "mean": 4568.5, "p50": 4770.6, "median": 4770.6, "p75": 6064.7, "p90": 6976.1, "p95": 7407.5, "p99": 8024.5, "p999": 8186.6}, "vusers.session_length": {"min": 1303.1, "max": 8590.5, "count": 178, "mean": 4639, "p50": 4867, "median": 4867, "p75": 6187.2, "p90": 7117, "p95": 7557.1, "p99": 8024.5, "p999": 8352}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 200, "vusers.created": 200, "http.requests": 200, "http.codes.200": 201, "http.responses": 201, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 201, "vusers.failed": 0, "vusers.completed": 201}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1716317670051, "firstHistogramAt": 1716317670051, "lastCounterAt": 1716317679999, "lastHistogramAt": 1716317679999, "firstMetricAt": 1716317670051, "lastMetricAt": 1716317679999, "period": "1716317670000", "summaries": {"http.response_time": {"min": 1978, "max": 9628, "count": 201, "mean": 5257.6, "p50": 6439.7, "median": 6439.7, "p75": 7117, "p90": 7709.8, "p95": 7865.6, "p99": 9230.4, "p999": 9416.8}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1978, "max": 9628, "count": 201, "mean": 5257.6, "p50": 6439.7, "median": 6439.7, "p75": 7117, "p90": 7709.8, "p95": 7865.6, "p99": 9230.4, "p999": 9416.8}, "vusers.session_length": {"min": 2038.6, "max": 9691.9, "count": 201, "mean": 5323.3, "p50": 6569.8, "median": 6569.8, "p75": 7260.8, "p90": 7709.8, "p95": 7865.6, "p99": 9416.8, "p999": 9416.8}}, "histograms": {"http.response_time": {"min": 1978, "max": 9628, "count": 201, "mean": 5257.6, "p50": 6439.7, "median": 6439.7, "p75": 7117, "p90": 7709.8, "p95": 7865.6, "p99": 9230.4, "p999": 9416.8}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1978, "max": 9628, "count": 201, "mean": 5257.6, "p50": 6439.7, "median": 6439.7, "p75": 7117, "p90": 7709.8, "p95": 7865.6, "p99": 9230.4, "p999": 9416.8}, "vusers.session_length": {"min": 2038.6, "max": 9691.9, "count": 201, "mean": 5323.3, "p50": 6569.8, "median": 6569.8, "p75": 7260.8, "p90": 7709.8, "p95": 7865.6, "p99": 9416.8, "p999": 9416.8}}}, {"counters": {"http.codes.200": 207, "http.responses": 207, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 207, "vusers.failed": 0, "vusers.completed": 207, "vusers.created_by_name.Scrape a URL": 200, "vusers.created": 200, "http.requests": 200}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1716317680071, "firstHistogramAt": 1716317680071, "lastCounterAt": 1716317689997, "lastHistogramAt": 1716317689954, "firstMetricAt": 1716317680071, "lastMetricAt": 1716317689997, "period": "1716317680000", "summaries": {"http.response_time": {"min": 2817, "max": 8921, "count": 207, "mean": 5136.1, "p50": 5487.5, "median": 5487.5, "p75": 6312.2, "p90": 6838, "p95": 6976.1, "p99": 7865.6, "p999": 8024.5}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 2817, "max": 8921, "count": 207, "mean": 5136.1, "p50": 5487.5, "median": 5487.5, "p75": 6312.2, "p90": 6838, "p95": 6976.1, "p99": 7865.6, "p999": 8024.5}, "vusers.session_length": {"min": 2892, "max": 8983.9, "count": 207, "mean": 5203.9, "p50": 5487.5, "median": 5487.5, "p75": 6312.2, "p90": 6838, "p95": 7117, "p99": 7865.6, "p999": 8024.5}}, "histograms": {"http.response_time": {"min": 2817, "max": 8921, "count": 207, "mean": 5136.1, "p50": 5487.5, "median": 5487.5, "p75": 6312.2, "p90": 6838, "p95": 6976.1, "p99": 7865.6, "p999": 8024.5}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 2817, "max": 8921, "count": 207, "mean": 5136.1, "p50": 5487.5, "median": 5487.5, "p75": 6312.2, "p90": 6838, "p95": 6976.1, "p99": 7865.6, "p999": 8024.5}, "vusers.session_length": {"min": 2892, "max": 8983.9, "count": 207, "mean": 5203.9, "p50": 5487.5, "median": 5487.5, "p75": 6312.2, "p90": 6838, "p95": 7117, "p99": 7865.6, "p999": 8024.5}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 200, "vusers.created": 200, "http.requests": 200, "http.codes.200": 177, "http.responses": 177, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 177, "vusers.failed": 0, "vusers.completed": 177}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1716317690045, "firstHistogramAt": 1716317690045, "lastCounterAt": 1716317699997, "lastHistogramAt": 1716317699997, "firstMetricAt": 1716317690045, "lastMetricAt": 1716317699997, "period": "1716317690000", "summaries": {"http.response_time": {"min": 1137, "max": 7206, "count": 177, "mean": 4873.3, "p50": 4867, "median": 4867, "p75": 5487.5, "p90": 6187.2, "p95": 6439.7, "p99": 6976.1, "p999": 7117}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1137, "max": 7206, "count": 177, "mean": 4873.3, "p50": 4867, "median": 4867, "p75": 5487.5, "p90": 6187.2, "p95": 6439.7, "p99": 6976.1, "p999": 7117}, "vusers.session_length": {"min": 1197.6, "max": 7268.8, "count": 177, "mean": 4941.8, "p50": 4965.3, "median": 4965.3, "p75": 5598.4, "p90": 6187.2, "p95": 6569.8, "p99": 6976.1, "p999": 7260.8}}, "histograms": {"http.response_time": {"min": 1137, "max": 7206, "count": 177, "mean": 4873.3, "p50": 4867, "median": 4867, "p75": 5487.5, "p90": 6187.2, "p95": 6439.7, "p99": 6976.1, "p999": 7117}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1137, "max": 7206, "count": 177, "mean": 4873.3, "p50": 4867, "median": 4867, "p75": 5487.5, "p90": 6187.2, "p95": 6439.7, "p99": 6976.1, "p999": 7117}, "vusers.session_length": {"min": 1197.6, "max": 7268.8, "count": 177, "mean": 4941.8, "p50": 4965.3, "median": 4965.3, "p75": 5598.4, "p90": 6187.2, "p95": 6569.8, "p99": 6976.1, "p999": 7260.8}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 200, "vusers.created": 200, "http.requests": 200, "http.codes.200": 194, "http.responses": 194, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 194, "vusers.failed": 0, "vusers.completed": 194}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1716317700044, "firstHistogramAt": 1716317700044, "lastCounterAt": 1716317709997, "lastHistogramAt": 1716317709958, "firstMetricAt": 1716317700044, "lastMetricAt": 1716317709997, "period": "1716317700000", "summaries": {"http.response_time": {"min": 1881, "max": 12494, "count": 194, "mean": 5875.9, "p50": 6838, "median": 6838, "p75": 8352, "p90": 9230.4, "p95": 9801.2, "p99": 10407.3, "p999": 11050.8}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1881, "max": 12494, "count": 194, "mean": 5875.9, "p50": 6838, "median": 6838, "p75": 8352, "p90": 9230.4, "p95": 9801.2, "p99": 10407.3, "p999": 11050.8}, "vusers.session_length": {"min": 1940.9, "max": 12551.3, "count": 194, "mean": 5947.7, "p50": 6976.1, "median": 6976.1, "p75": 8352, "p90": 9230.4, "p95": 9801.2, "p99": 10407.3, "p999": 11274.1}}, "histograms": {"http.response_time": {"min": 1881, "max": 12494, "count": 194, "mean": 5875.9, "p50": 6838, "median": 6838, "p75": 8352, "p90": 9230.4, "p95": 9801.2, "p99": 10407.3, "p999": 11050.8}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1881, "max": 12494, "count": 194, "mean": 5875.9, "p50": 6838, "median": 6838, "p75": 8352, "p90": 9230.4, "p95": 9801.2, "p99": 10407.3, "p999": 11050.8}, "vusers.session_length": {"min": 1940.9, "max": 12551.3, "count": 194, "mean": 5947.7, "p50": 6976.1, "median": 6976.1, "p75": 8352, "p90": 9230.4, "p95": 9801.2, "p99": 10407.3, "p999": 11274.1}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 200, "vusers.created": 200, "http.requests": 200, "http.codes.200": 202, "http.responses": 202, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 202, "vusers.failed": 0, "vusers.completed": 202}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1716317710025, "firstHistogramAt": 1716317710025, "lastCounterAt": 1716317719997, "lastHistogramAt": 1716317719969, "firstMetricAt": 1716317710025, "lastMetricAt": 1716317719997, "period": "1716317710000", "summaries": {"http.response_time": {"min": 3108, "max": 12775, "count": 202, "mean": 6378.6, "p50": 6976.1, "median": 6976.1, "p75": 8186.6, "p90": 9047.6, "p95": 9416.8, "p99": 11274.1, "p999": 11501.8}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 3108, "max": 12775, "count": 202, "mean": 6378.6, "p50": 6976.1, "median": 6976.1, "p75": 8186.6, "p90": 9047.6, "p95": 9416.8, "p99": 11274.1, "p999": 11501.8}, "vusers.session_length": {"min": 3175.8, "max": 12836.3, "count": 202, "mean": 6446.6, "p50": 7117, "median": 7117, "p75": 8186.6, "p90": 9047.6, "p95": 9607.1, "p99": 11274.1, "p999": 11734.2}}, "histograms": {"http.response_time": {"min": 3108, "max": 12775, "count": 202, "mean": 6378.6, "p50": 6976.1, "median": 6976.1, "p75": 8186.6, "p90": 9047.6, "p95": 9416.8, "p99": 11274.1, "p999": 11501.8}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 3108, "max": 12775, "count": 202, "mean": 6378.6, "p50": 6976.1, "median": 6976.1, "p75": 8186.6, "p90": 9047.6, "p95": 9416.8, "p99": 11274.1, "p999": 11501.8}, "vusers.session_length": {"min": 3175.8, "max": 12836.3, "count": 202, "mean": 6446.6, "p50": 7117, "median": 7117, "p75": 8186.6, "p90": 9047.6, "p95": 9607.1, "p99": 11274.1, "p999": 11734.2}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 200, "vusers.created": 200, "http.requests": 200, "http.codes.200": 188, "http.responses": 188, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 188, "vusers.failed": 0, "vusers.completed": 188}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1716317720023, "firstHistogramAt": 1716317720023, "lastCounterAt": 1716317729997, "lastHistogramAt": 1716317729957, "firstMetricAt": 1716317720023, "lastMetricAt": 1716317729997, "period": "1716317720000", "summaries": {"http.response_time": {"min": 1303, "max": 9329, "count": 188, "mean": 5781.8, "p50": 6064.7, "median": 6064.7, "p75": 7117, "p90": 7865.6, "p95": 8186.6, "p99": 8692.8, "p999": 9230.4}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1303, "max": 9329, "count": 188, "mean": 5781.8, "p50": 6064.7, "median": 6064.7, "p75": 7117, "p90": 7865.6, "p95": 8186.6, "p99": 8692.8, "p999": 9230.4}, "vusers.session_length": {"min": 1371.6, "max": 9395.4, "count": 188, "mean": 5849, "p50": 6187.2, "median": 6187.2, "p75": 7117, "p90": 7865.6, "p95": 8352, "p99": 8868.4, "p999": 9416.8}}, "histograms": {"http.response_time": {"min": 1303, "max": 9329, "count": 188, "mean": 5781.8, "p50": 6064.7, "median": 6064.7, "p75": 7117, "p90": 7865.6, "p95": 8186.6, "p99": 8692.8, "p999": 9230.4}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1303, "max": 9329, "count": 188, "mean": 5781.8, "p50": 6064.7, "median": 6064.7, "p75": 7117, "p90": 7865.6, "p95": 8186.6, "p99": 8692.8, "p999": 9230.4}, "vusers.session_length": {"min": 1371.6, "max": 9395.4, "count": 188, "mean": 5849, "p50": 6187.2, "median": 6187.2, "p75": 7117, "p90": 7865.6, "p95": 8352, "p99": 8868.4, "p999": 9416.8}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 200, "vusers.created": 200, "http.requests": 200, "http.codes.200": 197, "http.responses": 197, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 197, "vusers.failed": 0, "vusers.completed": 197}, "rates": {"http.request_rate": 20}, "http.request_rate": null, "firstCounterAt": 1716317730016, "firstHistogramAt": 1716317730016, "lastCounterAt": 1716317739998, "lastHistogramAt": 1716317739998, "firstMetricAt": 1716317730016, "lastMetricAt": 1716317739998, "period": "1716317730000", "summaries": {"http.response_time": {"min": 2031, "max": 15604, "count": 197, "mean": 7188.6, "p50": 8186.6, "median": 8186.6, "p75": 9047.6, "p90": 9999.2, "p95": 14048.5, "p99": 15218.6, "p999": 15218.6}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 2031, "max": 15604, "count": 197, "mean": 7188.6, "p50": 8186.6, "median": 8186.6, "p75": 9047.6, "p90": 9999.2, "p95": 14048.5, "p99": 15218.6, "p999": 15218.6}, "vusers.session_length": {"min": 2094.7, "max": 15666, "count": 197, "mean": 7256.9, "p50": 8352, "median": 8352, "p75": 9047.6, "p90": 9999.2, "p95": 14048.5, "p99": 15218.6, "p999": 15218.6}}, "histograms": {"http.response_time": {"min": 2031, "max": 15604, "count": 197, "mean": 7188.6, "p50": 8186.6, "median": 8186.6, "p75": 9047.6, "p90": 9999.2, "p95": 14048.5, "p99": 15218.6, "p999": 15218.6}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 2031, "max": 15604, "count": 197, "mean": 7188.6, "p50": 8186.6, "median": 8186.6, "p75": 9047.6, "p90": 9999.2, "p95": 14048.5, "p99": 15218.6, "p999": 15218.6}, "vusers.session_length": {"min": 2094.7, "max": 15666, "count": 197, "mean": 7256.9, "p50": 8352, "median": 8352, "p75": 9047.6, "p90": 9999.2, "p95": 14048.5, "p99": 15218.6, "p999": 15218.6}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 233, "vusers.created": 233, "http.requests": 233, "http.codes.200": 211, "http.responses": 212, "http.downloaded_bytes": 0, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 211, "vusers.failed": 1, "vusers.completed": 211, "http.codes.502": 1, "plugins.metrics-by-endpoint./v0/scrape.codes.502": 1, "errors.Failed capture or match": 1}, "rates": {"http.request_rate": 23}, "http.request_rate": null, "firstCounterAt": 1716317740011, "firstHistogramAt": 1716317740011, "lastCounterAt": 1716317749963, "lastHistogramAt": 1716317749438, "firstMetricAt": 1716317740011, "lastMetricAt": 1716317749963, "period": "1716317740000", "summaries": {"http.response_time": {"min": 281, "max": 15578, "count": 212, "mean": 5976.4, "p50": 5598.4, "median": 5598.4, "p75": 8186.6, "p90": 8692.8, "p95": 9047.6, "p99": 9801.2, "p999": 9801.2}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 281, "max": 15578, "count": 212, "mean": 5976.4, "p50": 5598.4, "median": 5598.4, "p75": 8186.6, "p90": 8692.8, "p95": 9047.6, "p99": 9801.2, "p999": 9801.2}, "vusers.session_length": {"min": 1174.3, "max": 15643.2, "count": 211, "mean": 6069.3, "p50": 5826.9, "median": 5826.9, "p75": 8186.6, "p90": 8868.4, "p95": 9047.6, "p99": 9801.2, "p999": 9999.2}}, "histograms": {"http.response_time": {"min": 281, "max": 15578, "count": 212, "mean": 5976.4, "p50": 5598.4, "median": 5598.4, "p75": 8186.6, "p90": 8692.8, "p95": 9047.6, "p99": 9801.2, "p999": 9801.2}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 281, "max": 15578, "count": 212, "mean": 5976.4, "p50": 5598.4, "median": 5598.4, "p75": 8186.6, "p90": 8692.8, "p95": 9047.6, "p99": 9801.2, "p999": 9801.2}, "vusers.session_length": {"min": 1174.3, "max": 15643.2, "count": 211, "mean": 6069.3, "p50": 5826.9, "median": 5826.9, "p75": 8186.6, "p90": 8868.4, "p95": 9047.6, "p99": 9801.2, "p999": 9999.2}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 300, "vusers.created": 300, "http.requests": 300, "http.codes.200": 297, "http.responses": 297, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 297, "vusers.failed": 0, "vusers.completed": 297}, "rates": {"http.request_rate": 30}, "http.request_rate": null, "firstCounterAt": 1716317750007, "firstHistogramAt": 1716317750070, "lastCounterAt": 1716317759985, "lastHistogramAt": 1716317759985, "firstMetricAt": 1716317750007, "lastMetricAt": 1716317759985, "period": "1716317750000", "summaries": {"http.response_time": {"min": 2703, "max": 13170, "count": 297, "mean": 6026.6, "p50": 4867, "median": 4867, "p75": 8352, "p90": 9416.8, "p95": 10407.3, "p99": 12459.8, "p999": 12968.3}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 2703, "max": 13170, "count": 297, "mean": 6026.6, "p50": 4867, "median": 4867, "p75": 8352, "p90": 9416.8, "p95": 10407.3, "p99": 12459.8, "p999": 12968.3}, "vusers.session_length": {"min": 2765.7, "max": 13238.5, "count": 297, "mean": 6096.6, "p50": 4867, "median": 4867, "p75": 8352, "p90": 9416.8, "p95": 10407.3, "p99": 12459.8, "p999": 12968.3}}, "histograms": {"http.response_time": {"min": 2703, "max": 13170, "count": 297, "mean": 6026.6, "p50": 4867, "median": 4867, "p75": 8352, "p90": 9416.8, "p95": 10407.3, "p99": 12459.8, "p999": 12968.3}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 2703, "max": 13170, "count": 297, "mean": 6026.6, "p50": 4867, "median": 4867, "p75": 8352, "p90": 9416.8, "p95": 10407.3, "p99": 12459.8, "p999": 12968.3}, "vusers.session_length": {"min": 2765.7, "max": 13238.5, "count": 297, "mean": 6096.6, "p50": 4867, "median": 4867, "p75": 8352, "p90": 9416.8, "p95": 10407.3, "p99": 12459.8, "p999": 12968.3}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 300, "vusers.created": 300, "http.requests": 300, "http.codes.200": 310, "http.responses": 311, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 310, "vusers.failed": 1, "vusers.completed": 310, "http.downloaded_bytes": 0, "http.codes.502": 1, "plugins.metrics-by-endpoint./v0/scrape.codes.502": 1, "errors.Failed capture or match": 1}, "rates": {"http.request_rate": 30}, "http.request_rate": null, "firstCounterAt": 1716317760001, "firstHistogramAt": 1716317760001, "lastCounterAt": 1716317769967, "lastHistogramAt": 1716317769967, "firstMetricAt": 1716317760001, "lastMetricAt": 1716317769967, "period": "1716317760000", "summaries": {"http.response_time": {"min": 65, "max": 8014, "count": 311, "mean": 5296.5, "p50": 5272.4, "median": 5272.4, "p75": 5711.5, "p90": 6439.7, "p95": 7117, "p99": 7865.6, "p999": 8024.5}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 65, "max": 8014, "count": 311, "mean": 5296.5, "p50": 5272.4, "median": 5272.4, "p75": 5711.5, "p90": 6439.7, "p95": 7117, "p99": 7865.6, "p999": 8024.5}, "vusers.session_length": {"min": 3374.9, "max": 8078.7, "count": 310, "mean": 5386.1, "p50": 5272.4, "median": 5272.4, "p75": 5826.9, "p90": 6569.8, "p95": 7260.8, "p99": 8024.5, "p999": 8024.5}}, "histograms": {"http.response_time": {"min": 65, "max": 8014, "count": 311, "mean": 5296.5, "p50": 5272.4, "median": 5272.4, "p75": 5711.5, "p90": 6439.7, "p95": 7117, "p99": 7865.6, "p999": 8024.5}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 65, "max": 8014, "count": 311, "mean": 5296.5, "p50": 5272.4, "median": 5272.4, "p75": 5711.5, "p90": 6439.7, "p95": 7117, "p99": 7865.6, "p999": 8024.5}, "vusers.session_length": {"min": 3374.9, "max": 8078.7, "count": 310, "mean": 5386.1, "p50": 5272.4, "median": 5272.4, "p75": 5826.9, "p90": 6569.8, "p95": 7260.8, "p99": 8024.5, "p999": 8024.5}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 300, "vusers.created": 300, "http.requests": 300, "http.codes.200": 299, "http.responses": 299, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 299, "vusers.failed": 0, "vusers.completed": 299}, "rates": {"http.request_rate": 30}, "http.request_rate": null, "firstCounterAt": 1716317770007, "firstHistogramAt": 1716317770027, "lastCounterAt": 1716317779996, "lastHistogramAt": 1716317779996, "firstMetricAt": 1716317770007, "lastMetricAt": 1716317779996, "period": "1716317770000", "summaries": {"http.response_time": {"min": 3256, "max": 7236, "count": 299, "mean": 4982.3, "p50": 4867, "median": 4867, "p75": 5487.5, "p90": 6064.7, "p95": 6312.2, "p99": 6702.6, "p999": 6702.6}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 3256, "max": 7236, "count": 299, "mean": 4982.3, "p50": 4867, "median": 4867, "p75": 5487.5, "p90": 6064.7, "p95": 6312.2, "p99": 6702.6, "p999": 6702.6}, "vusers.session_length": {"min": 3320.9, "max": 7299.4, "count": 299, "mean": 5054.7, "p50": 4867, "median": 4867, "p75": 5598.4, "p90": 6187.2, "p95": 6439.7, "p99": 6702.6, "p999": 6838}}, "histograms": {"http.response_time": {"min": 3256, "max": 7236, "count": 299, "mean": 4982.3, "p50": 4867, "median": 4867, "p75": 5487.5, "p90": 6064.7, "p95": 6312.2, "p99": 6702.6, "p999": 6702.6}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 3256, "max": 7236, "count": 299, "mean": 4982.3, "p50": 4867, "median": 4867, "p75": 5487.5, "p90": 6064.7, "p95": 6312.2, "p99": 6702.6, "p999": 6702.6}, "vusers.session_length": {"min": 3320.9, "max": 7299.4, "count": 299, "mean": 5054.7, "p50": 4867, "median": 4867, "p75": 5598.4, "p90": 6187.2, "p95": 6439.7, "p99": 6702.6, "p999": 6838}}}, {"counters": {"http.codes.200": 309, "http.responses": 311, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 309, "vusers.failed": 2, "vusers.completed": 309, "vusers.created_by_name.Scrape a URL": 300, "vusers.created": 300, "http.requests": 300, "http.downloaded_bytes": 0, "http.codes.502": 2, "plugins.metrics-by-endpoint./v0/scrape.codes.502": 2, "errors.Failed capture or match": 2}, "rates": {"http.request_rate": 30}, "http.request_rate": null, "firstCounterAt": 1716317780001, "firstHistogramAt": 1716317780001, "lastCounterAt": 1716317789981, "lastHistogramAt": 1716317789981, "firstMetricAt": 1716317780001, "lastMetricAt": 1716317789981, "period": "1716317780000", "summaries": {"http.response_time": {"min": 62, "max": 8291, "count": 311, "mean": 4825.5, "p50": 4583.6, "median": 4583.6, "p75": 5378.9, "p90": 6569.8, "p95": 6838, "p99": 7709.8, "p999": 7709.8}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 62, "max": 8291, "count": 311, "mean": 4825.5, "p50": 4583.6, "median": 4583.6, "p75": 5378.9, "p90": 6569.8, "p95": 6838, "p99": 7709.8, "p999": 7709.8}, "vusers.session_length": {"min": 3281.2, "max": 8351.9, "count": 309, "mean": 4925.2, "p50": 4676.2, "median": 4676.2, "p75": 5487.5, "p90": 6569.8, "p95": 6838, "p99": 7709.8, "p999": 7865.6}}, "histograms": {"http.response_time": {"min": 62, "max": 8291, "count": 311, "mean": 4825.5, "p50": 4583.6, "median": 4583.6, "p75": 5378.9, "p90": 6569.8, "p95": 6838, "p99": 7709.8, "p999": 7709.8}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 62, "max": 8291, "count": 311, "mean": 4825.5, "p50": 4583.6, "median": 4583.6, "p75": 5378.9, "p90": 6569.8, "p95": 6838, "p99": 7709.8, "p999": 7709.8}, "vusers.session_length": {"min": 3281.2, "max": 8351.9, "count": 309, "mean": 4925.2, "p50": 4676.2, "median": 4676.2, "p75": 5487.5, "p90": 6569.8, "p95": 6838, "p99": 7709.8, "p999": 7865.6}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 300, "vusers.created": 300, "http.requests": 300, "http.codes.200": 298, "http.responses": 298, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 298, "vusers.failed": 0, "vusers.completed": 298}, "rates": {"http.request_rate": 30}, "http.request_rate": null, "firstCounterAt": 1716317790007, "firstHistogramAt": 1716317790053, "lastCounterAt": 1716317799988, "lastHistogramAt": 1716317799988, "firstMetricAt": 1716317790007, "lastMetricAt": 1716317799988, "period": "1716317790000", "summaries": {"http.response_time": {"min": 2525, "max": 7325, "count": 298, "mean": 4693.4, "p50": 4492.8, "median": 4492.8, "p75": 5598.4, "p90": 6439.7, "p95": 6702.6, "p99": 7260.8, "p999": 7260.8}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 2525, "max": 7325, "count": 298, "mean": 4693.4, "p50": 4492.8, "median": 4492.8, "p75": 5598.4, "p90": 6439.7, "p95": 6702.6, "p99": 7260.8, "p999": 7260.8}, "vusers.session_length": {"min": 2597.3, "max": 7411.8, "count": 298, "mean": 4765, "p50": 4583.6, "median": 4583.6, "p75": 5598.4, "p90": 6439.7, "p95": 6838, "p99": 7260.8, "p999": 7407.5}}, "histograms": {"http.response_time": {"min": 2525, "max": 7325, "count": 298, "mean": 4693.4, "p50": 4492.8, "median": 4492.8, "p75": 5598.4, "p90": 6439.7, "p95": 6702.6, "p99": 7260.8, "p999": 7260.8}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 2525, "max": 7325, "count": 298, "mean": 4693.4, "p50": 4492.8, "median": 4492.8, "p75": 5598.4, "p90": 6439.7, "p95": 6702.6, "p99": 7260.8, "p999": 7260.8}, "vusers.session_length": {"min": 2597.3, "max": 7411.8, "count": 298, "mean": 4765, "p50": 4583.6, "median": 4583.6, "p75": 5598.4, "p90": 6439.7, "p95": 6838, "p99": 7260.8, "p999": 7407.5}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 300, "vusers.created": 300, "http.requests": 300, "http.codes.200": 294, "http.responses": 294, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 294, "vusers.failed": 0, "vusers.completed": 294}, "rates": {"http.request_rate": 30}, "http.request_rate": null, "firstCounterAt": 1716317800004, "firstHistogramAt": 1716317800004, "lastCounterAt": 1716317809963, "lastHistogramAt": 1716317809889, "firstMetricAt": 1716317800004, "lastMetricAt": 1716317809963, "period": "1716317800000", "summaries": {"http.response_time": {"min": 2639, "max": 7453, "count": 294, "mean": 5093.8, "p50": 5168, "median": 5168, "p75": 5598.4, "p90": 6064.7, "p95": 6439.7, "p99": 6976.1, "p999": 7117}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 2639, "max": 7453, "count": 294, "mean": 5093.8, "p50": 5168, "median": 5168, "p75": 5598.4, "p90": 6064.7, "p95": 6439.7, "p99": 6976.1, "p999": 7117}, "vusers.session_length": {"min": 2699.9, "max": 7518.9, "count": 294, "mean": 5164.6, "p50": 5272.4, "median": 5272.4, "p75": 5711.5, "p90": 6187.2, "p95": 6439.7, "p99": 7117, "p999": 7117}}, "histograms": {"http.response_time": {"min": 2639, "max": 7453, "count": 294, "mean": 5093.8, "p50": 5168, "median": 5168, "p75": 5598.4, "p90": 6064.7, "p95": 6439.7, "p99": 6976.1, "p999": 7117}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 2639, "max": 7453, "count": 294, "mean": 5093.8, "p50": 5168, "median": 5168, "p75": 5598.4, "p90": 6064.7, "p95": 6439.7, "p99": 6976.1, "p999": 7117}, "vusers.session_length": {"min": 2699.9, "max": 7518.9, "count": 294, "mean": 5164.6, "p50": 5272.4, "median": 5272.4, "p75": 5711.5, "p90": 6187.2, "p95": 6439.7, "p99": 7117, "p999": 7117}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 300, "vusers.created": 300, "http.requests": 300, "http.codes.200": 302, "http.responses": 302, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 302, "vusers.failed": 0, "vusers.completed": 302}, "rates": {"http.request_rate": 30}, "http.request_rate": null, "firstCounterAt": 1716317810006, "firstHistogramAt": 1716317810059, "lastCounterAt": 1716317819987, "lastHistogramAt": 1716317819987, "firstMetricAt": 1716317810006, "lastMetricAt": 1716317819987, "period": "1716317810000", "summaries": {"http.response_time": {"min": 3094, "max": 6873, "count": 302, "mean": 4973.4, "p50": 4965.3, "median": 4965.3, "p75": 5378.9, "p90": 5826.9, "p95": 5944.6, "p99": 6439.7, "p999": 6569.8}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 3094, "max": 6873, "count": 302, "mean": 4973.4, "p50": 4965.3, "median": 4965.3, "p75": 5378.9, "p90": 5826.9, "p95": 5944.6, "p99": 6439.7, "p999": 6569.8}, "vusers.session_length": {"min": 3170.7, "max": 6932.6, "count": 302, "mean": 5042.1, "p50": 5065.6, "median": 5065.6, "p75": 5487.5, "p90": 5826.9, "p95": 5944.6, "p99": 6439.7, "p999": 6702.6}}, "histograms": {"http.response_time": {"min": 3094, "max": 6873, "count": 302, "mean": 4973.4, "p50": 4965.3, "median": 4965.3, "p75": 5378.9, "p90": 5826.9, "p95": 5944.6, "p99": 6439.7, "p999": 6569.8}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 3094, "max": 6873, "count": 302, "mean": 4973.4, "p50": 4965.3, "median": 4965.3, "p75": 5378.9, "p90": 5826.9, "p95": 5944.6, "p99": 6439.7, "p999": 6569.8}, "vusers.session_length": {"min": 3170.7, "max": 6932.6, "count": 302, "mean": 5042.1, "p50": 5065.6, "median": 5065.6, "p75": 5487.5, "p90": 5826.9, "p95": 5944.6, "p99": 6439.7, "p999": 6702.6}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 300, "vusers.created": 300, "http.requests": 300, "http.codes.200": 292, "http.responses": 292, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 292, "vusers.failed": 0, "vusers.completed": 292}, "rates": {"http.request_rate": 30}, "http.request_rate": null, "firstCounterAt": 1716317820006, "firstHistogramAt": 1716317820038, "lastCounterAt": 1716317829982, "lastHistogramAt": 1716317829982, "firstMetricAt": 1716317820006, "lastMetricAt": 1716317829982, "period": "1716317820000", "summaries": {"http.response_time": {"min": 2810, "max": 10893, "count": 292, "mean": 5125.7, "p50": 4965.3, "median": 4965.3, "p75": 5826.9, "p90": 6439.7, "p95": 6702.6, "p99": 7557.1, "p999": 8024.5}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 2810, "max": 10893, "count": 292, "mean": 5125.7, "p50": 4965.3, "median": 4965.3, "p75": 5826.9, "p90": 6439.7, "p95": 6702.6, "p99": 7557.1, "p999": 8024.5}, "vusers.session_length": {"min": 2879.2, "max": 10953.5, "count": 292, "mean": 5197.3, "p50": 5065.6, "median": 5065.6, "p75": 5826.9, "p90": 6439.7, "p95": 6838, "p99": 7709.8, "p999": 8186.6}}, "histograms": {"http.response_time": {"min": 2810, "max": 10893, "count": 292, "mean": 5125.7, "p50": 4965.3, "median": 4965.3, "p75": 5826.9, "p90": 6439.7, "p95": 6702.6, "p99": 7557.1, "p999": 8024.5}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 2810, "max": 10893, "count": 292, "mean": 5125.7, "p50": 4965.3, "median": 4965.3, "p75": 5826.9, "p90": 6439.7, "p95": 6702.6, "p99": 7557.1, "p999": 8024.5}, "vusers.session_length": {"min": 2879.2, "max": 10953.5, "count": 292, "mean": 5197.3, "p50": 5065.6, "median": 5065.6, "p75": 5826.9, "p90": 6439.7, "p95": 6838, "p99": 7709.8, "p999": 8186.6}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 300, "vusers.created": 300, "http.requests": 300, "http.codes.200": 280, "http.responses": 280, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 280, "vusers.failed": 0, "vusers.completed": 280}, "rates": {"http.request_rate": 30}, "http.request_rate": null, "firstCounterAt": 1716317830003, "firstHistogramAt": 1716317830003, "lastCounterAt": 1716317839994, "lastHistogramAt": 1716317839994, "firstMetricAt": 1716317830003, "lastMetricAt": 1716317839994, "period": "1716317830000", "summaries": {"http.response_time": {"min": 2052, "max": 8277, "count": 280, "mean": 5448.2, "p50": 5598.4, "median": 5598.4, "p75": 6312.2, "p90": 6838, "p95": 7260.8, "p99": 7709.8, "p999": 7865.6}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 2052, "max": 8277, "count": 280, "mean": 5448.2, "p50": 5598.4, "median": 5598.4, "p75": 6312.2, "p90": 6838, "p95": 7260.8, "p99": 7709.8, "p999": 7865.6}, "vusers.session_length": {"min": 2119.8, "max": 8344.4, "count": 280, "mean": 5516.7, "p50": 5711.5, "median": 5711.5, "p75": 6439.7, "p90": 6976.1, "p95": 7260.8, "p99": 7865.6, "p999": 8024.5}}, "histograms": {"http.response_time": {"min": 2052, "max": 8277, "count": 280, "mean": 5448.2, "p50": 5598.4, "median": 5598.4, "p75": 6312.2, "p90": 6838, "p95": 7260.8, "p99": 7709.8, "p999": 7865.6}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 2052, "max": 8277, "count": 280, "mean": 5448.2, "p50": 5598.4, "median": 5598.4, "p75": 6312.2, "p90": 6838, "p95": 7260.8, "p99": 7709.8, "p999": 7865.6}, "vusers.session_length": {"min": 2119.8, "max": 8344.4, "count": 280, "mean": 5516.7, "p50": 5711.5, "median": 5711.5, "p75": 6439.7, "p90": 6976.1, "p95": 7260.8, "p99": 7865.6, "p999": 8024.5}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 300, "vusers.created": 300, "http.requests": 300, "http.codes.200": 305, "http.responses": 305, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 305, "vusers.failed": 0, "vusers.completed": 305}, "rates": {"http.request_rate": 30}, "http.request_rate": null, "firstCounterAt": 1716317840006, "firstHistogramAt": 1716317840044, "lastCounterAt": 1716317849988, "lastHistogramAt": 1716317849988, "firstMetricAt": 1716317840006, "lastMetricAt": 1716317849988, "period": "1716317840000", "summaries": {"http.response_time": {"min": 3239, "max": 9192, "count": 305, "mean": 5936.4, "p50": 6064.7, "median": 6064.7, "p75": 6838, "p90": 7407.5, "p95": 7709.8, "p99": 8352, "p999": 8520.7}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 3239, "max": 9192, "count": 305, "mean": 5936.4, "p50": 6064.7, "median": 6064.7, "p75": 6838, "p90": 7407.5, "p95": 7709.8, "p99": 8352, "p999": 8520.7}, "vusers.session_length": {"min": 3301.9, "max": 9430.5, "count": 305, "mean": 6005.9, "p50": 6187.2, "median": 6187.2, "p75": 6976.1, "p90": 7557.1, "p95": 7709.8, "p99": 8352, "p999": 8520.7}}, "histograms": {"http.response_time": {"min": 3239, "max": 9192, "count": 305, "mean": 5936.4, "p50": 6064.7, "median": 6064.7, "p75": 6838, "p90": 7407.5, "p95": 7709.8, "p99": 8352, "p999": 8520.7}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 3239, "max": 9192, "count": 305, "mean": 5936.4, "p50": 6064.7, "median": 6064.7, "p75": 6838, "p90": 7407.5, "p95": 7709.8, "p99": 8352, "p999": 8520.7}, "vusers.session_length": {"min": 3301.9, "max": 9430.5, "count": 305, "mean": 6005.9, "p50": 6187.2, "median": 6187.2, "p75": 6976.1, "p90": 7557.1, "p95": 7709.8, "p99": 8352, "p999": 8520.7}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 300, "vusers.created": 300, "http.requests": 300, "http.codes.200": 277, "http.responses": 277, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 277, "vusers.failed": 0, "vusers.completed": 277}, "rates": {"http.request_rate": 30}, "http.request_rate": null, "firstCounterAt": 1716317850006, "firstHistogramAt": 1716317850024, "lastCounterAt": 1716317859962, "lastHistogramAt": 1716317859927, "firstMetricAt": 1716317850006, "lastMetricAt": 1716317859962, "period": "1716317850000", "summaries": {"http.response_time": {"min": 4402, "max": 8245, "count": 277, "mean": 6261.7, "p50": 6187.2, "median": 6187.2, "p75": 6838, "p90": 7407.5, "p95": 7709.8, "p99": 8024.5, "p999": 8186.6}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 4402, "max": 8245, "count": 277, "mean": 6261.7, "p50": 6187.2, "median": 6187.2, "p75": 6838, "p90": 7407.5, "p95": 7709.8, "p99": 8024.5, "p999": 8186.6}, "vusers.session_length": {"min": 4467.8, "max": 8866.9, "count": 277, "mean": 6368.3, "p50": 6312.2, "median": 6312.2, "p75": 6976.1, "p90": 7557.1, "p95": 7865.6, "p99": 8352, "p999": 8352}}, "histograms": {"http.response_time": {"min": 4402, "max": 8245, "count": 277, "mean": 6261.7, "p50": 6187.2, "median": 6187.2, "p75": 6838, "p90": 7407.5, "p95": 7709.8, "p99": 8024.5, "p999": 8186.6}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 4402, "max": 8245, "count": 277, "mean": 6261.7, "p50": 6187.2, "median": 6187.2, "p75": 6838, "p90": 7407.5, "p95": 7709.8, "p99": 8024.5, "p999": 8186.6}, "vusers.session_length": {"min": 4467.8, "max": 8866.9, "count": 277, "mean": 6368.3, "p50": 6312.2, "median": 6312.2, "p75": 6976.1, "p90": 7557.1, "p95": 7865.6, "p99": 8352, "p999": 8352}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 300, "vusers.created": 300, "http.requests": 300, "http.codes.200": 265, "http.responses": 265, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 265, "vusers.failed": 0, "vusers.completed": 265}, "rates": {"http.request_rate": 30}, "http.request_rate": null, "firstCounterAt": 1716317860003, "firstHistogramAt": 1716317860003, "lastCounterAt": 1716317869962, "lastHistogramAt": 1716317869960, "firstMetricAt": 1716317860003, "lastMetricAt": 1716317869962, "period": "1716317860000", "summaries": {"http.response_time": {"min": 5140, "max": 9746, "count": 265, "mean": 6978.4, "p50": 6976.1, "median": 6976.1, "p75": 7865.6, "p90": 8520.7, "p95": 8868.4, "p99": 9416.8, "p999": 9607.1}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 5140, "max": 9746, "count": 265, "mean": 6978.4, "p50": 6976.1, "median": 6976.1, "p75": 7865.6, "p90": 8520.7, "p95": 8868.4, "p99": 9416.8, "p999": 9607.1}, "vusers.session_length": {"min": 5206.9, "max": 9806.3, "count": 265, "mean": 7048, "p50": 6976.1, "median": 6976.1, "p75": 7865.6, "p90": 8520.7, "p95": 9047.6, "p99": 9416.8, "p999": 9607.1}}, "histograms": {"http.response_time": {"min": 5140, "max": 9746, "count": 265, "mean": 6978.4, "p50": 6976.1, "median": 6976.1, "p75": 7865.6, "p90": 8520.7, "p95": 8868.4, "p99": 9416.8, "p999": 9607.1}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 5140, "max": 9746, "count": 265, "mean": 6978.4, "p50": 6976.1, "median": 6976.1, "p75": 7865.6, "p90": 8520.7, "p95": 8868.4, "p99": 9416.8, "p999": 9607.1}, "vusers.session_length": {"min": 5206.9, "max": 9806.3, "count": 265, "mean": 7048, "p50": 6976.1, "median": 6976.1, "p75": 7865.6, "p90": 8520.7, "p95": 9047.6, "p99": 9416.8, "p999": 9607.1}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 300, "vusers.created": 300, "http.requests": 300, "http.codes.200": 266, "http.responses": 266, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 266, "vusers.failed": 0, "vusers.completed": 266}, "rates": {"http.request_rate": 30}, "http.request_rate": null, "firstCounterAt": 1716317870006, "firstHistogramAt": 1716317870030, "lastCounterAt": 1716317879962, "lastHistogramAt": 1716317879952, "firstMetricAt": 1716317870006, "lastMetricAt": 1716317879962, "period": "1716317870000", "summaries": {"http.response_time": {"min": 2222, "max": 17282, "count": 266, "mean": 7690, "p50": 7709.8, "median": 7709.8, "p75": 9416.8, "p90": 9801.2, "p95": 10201.2, "p99": 16819.2, "p999": 17158.9}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 2222, "max": 17282, "count": 266, "mean": 7690, "p50": 7709.8, "median": 7709.8, "p75": 9416.8, "p90": 9801.2, "p95": 10201.2, "p99": 16819.2, "p999": 17158.9}, "vusers.session_length": {"min": 2280.8, "max": 17349.7, "count": 266, "mean": 7756.7, "p50": 7865.6, "median": 7865.6, "p75": 9416.8, "p90": 9999.2, "p95": 10201.2, "p99": 16819.2, "p999": 17505.6}}, "histograms": {"http.response_time": {"min": 2222, "max": 17282, "count": 266, "mean": 7690, "p50": 7709.8, "median": 7709.8, "p75": 9416.8, "p90": 9801.2, "p95": 10201.2, "p99": 16819.2, "p999": 17158.9}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 2222, "max": 17282, "count": 266, "mean": 7690, "p50": 7709.8, "median": 7709.8, "p75": 9416.8, "p90": 9801.2, "p95": 10201.2, "p99": 16819.2, "p999": 17158.9}, "vusers.session_length": {"min": 2280.8, "max": 17349.7, "count": 266, "mean": 7756.7, "p50": 7865.6, "median": 7865.6, "p75": 9416.8, "p90": 9999.2, "p95": 10201.2, "p99": 16819.2, "p999": 17505.6}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 300, "vusers.created": 300, "http.requests": 300, "http.codes.200": 275, "http.responses": 275, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 275, "vusers.failed": 0, "vusers.completed": 275}, "rates": {"http.request_rate": 30}, "http.request_rate": null, "firstCounterAt": 1716317880006, "firstHistogramAt": 1716317880006, "lastCounterAt": 1716317889973, "lastHistogramAt": 1716317889973, "firstMetricAt": 1716317880006, "lastMetricAt": 1716317889973, "period": "1716317880000", "summaries": {"http.response_time": {"min": 3919, "max": 18924, "count": 275, "mean": 9134.3, "p50": 9047.6, "median": 9047.6, "p75": 10617.5, "p90": 11501.8, "p95": 11971.2, "p99": 18588.1, "p999": 18963.6}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 3919, "max": 18924, "count": 275, "mean": 9134.3, "p50": 9047.6, "median": 9047.6, "p75": 10617.5, "p90": 11501.8, "p95": 11971.2, "p99": 18588.1, "p999": 18963.6}, "vusers.session_length": {"min": 3985.8, "max": 18980.3, "count": 275, "mean": 9205.9, "p50": 9047.6, "median": 9047.6, "p75": 10617.5, "p90": 11734.2, "p95": 11971.2, "p99": 18588.1, "p999": 18963.6}}, "histograms": {"http.response_time": {"min": 3919, "max": 18924, "count": 275, "mean": 9134.3, "p50": 9047.6, "median": 9047.6, "p75": 10617.5, "p90": 11501.8, "p95": 11971.2, "p99": 18588.1, "p999": 18963.6}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 3919, "max": 18924, "count": 275, "mean": 9134.3, "p50": 9047.6, "median": 9047.6, "p75": 10617.5, "p90": 11501.8, "p95": 11971.2, "p99": 18588.1, "p999": 18963.6}, "vusers.session_length": {"min": 3985.8, "max": 18980.3, "count": 275, "mean": 9205.9, "p50": 9047.6, "median": 9047.6, "p75": 10617.5, "p90": 11734.2, "p95": 11971.2, "p99": 18588.1, "p999": 18963.6}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 300, "vusers.created": 300, "http.requests": 300, "http.codes.200": 309, "http.responses": 309, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 309, "vusers.failed": 0, "vusers.completed": 309}, "rates": {"http.request_rate": 30}, "http.request_rate": null, "firstCounterAt": 1716317890006, "firstHistogramAt": 1716317890041, "lastCounterAt": 1716317899970, "lastHistogramAt": 1716317899970, "firstMetricAt": 1716317890006, "lastMetricAt": 1716317899970, "period": "1716317890000", "summaries": {"http.response_time": {"min": 5387, "max": 14520, "count": 309, "mean": 9244.4, "p50": 9416.8, "median": 9416.8, "p75": 10617.5, "p90": 11734.2, "p95": 12459.8, "p99": 13230.3, "p999": 14048.5}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 5387, "max": 14520, "count": 309, "mean": 9244.4, "p50": 9416.8, "median": 9416.8, "p75": 10617.5, "p90": 11734.2, "p95": 12459.8, "p99": 13230.3, "p999": 14048.5}, "vusers.session_length": {"min": 5454.9, "max": 14581.4, "count": 309, "mean": 9313.9, "p50": 9416.8, "median": 9416.8, "p75": 10617.5, "p90": 11734.2, "p95": 12459.8, "p99": 13230.3, "p999": 14332.3}}, "histograms": {"http.response_time": {"min": 5387, "max": 14520, "count": 309, "mean": 9244.4, "p50": 9416.8, "median": 9416.8, "p75": 10617.5, "p90": 11734.2, "p95": 12459.8, "p99": 13230.3, "p999": 14048.5}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 5387, "max": 14520, "count": 309, "mean": 9244.4, "p50": 9416.8, "median": 9416.8, "p75": 10617.5, "p90": 11734.2, "p95": 12459.8, "p99": 13230.3, "p999": 14048.5}, "vusers.session_length": {"min": 5454.9, "max": 14581.4, "count": 309, "mean": 9313.9, "p50": 9416.8, "median": 9416.8, "p75": 10617.5, "p90": 11734.2, "p95": 12459.8, "p99": 13230.3, "p999": 14332.3}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 300, "vusers.created": 300, "http.requests": 300, "http.codes.200": 291, "http.responses": 291, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 291, "vusers.failed": 0, "vusers.completed": 291}, "rates": {"http.request_rate": 30}, "http.request_rate": null, "firstCounterAt": 1716317900006, "firstHistogramAt": 1716317900042, "lastCounterAt": 1716317909969, "lastHistogramAt": 1716317909969, "firstMetricAt": 1716317900006, "lastMetricAt": 1716317909969, "period": "1716317900000", "summaries": {"http.response_time": {"min": 6183, "max": 13707, "count": 291, "mean": 9565.6, "p50": 9607.1, "median": 9607.1, "p75": 11050.8, "p90": 12459.8, "p95": 12711.5, "p99": 13230.3, "p999": 13497.6}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 6183, "max": 13707, "count": 291, "mean": 9565.6, "p50": 9607.1, "median": 9607.1, "p75": 11050.8, "p90": 12459.8, "p95": 12711.5, "p99": 13230.3, "p999": 13497.6}, "vusers.session_length": {"min": 6243.4, "max": 13769.8, "count": 291, "mean": 9636.8, "p50": 9607.1, "median": 9607.1, "p75": 11050.8, "p90": 12459.8, "p95": 12711.5, "p99": 13497.6, "p999": 13497.6}}, "histograms": {"http.response_time": {"min": 6183, "max": 13707, "count": 291, "mean": 9565.6, "p50": 9607.1, "median": 9607.1, "p75": 11050.8, "p90": 12459.8, "p95": 12711.5, "p99": 13230.3, "p999": 13497.6}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 6183, "max": 13707, "count": 291, "mean": 9565.6, "p50": 9607.1, "median": 9607.1, "p75": 11050.8, "p90": 12459.8, "p95": 12711.5, "p99": 13230.3, "p999": 13497.6}, "vusers.session_length": {"min": 6243.4, "max": 13769.8, "count": 291, "mean": 9636.8, "p50": 9607.1, "median": 9607.1, "p75": 11050.8, "p90": 12459.8, "p95": 12711.5, "p99": 13497.6, "p999": 13497.6}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 300, "vusers.created": 300, "http.requests": 300, "http.codes.200": 296, "http.responses": 296, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 296, "vusers.failed": 0, "vusers.completed": 296}, "rates": {"http.request_rate": 30}, "http.request_rate": null, "firstCounterAt": 1716317910006, "firstHistogramAt": 1716317910043, "lastCounterAt": 1716317919978, "lastHistogramAt": 1716317919978, "firstMetricAt": 1716317910006, "lastMetricAt": 1716317919978, "period": "1716317910000", "summaries": {"http.response_time": {"min": 5766, "max": 15382, "count": 296, "mean": 9946.1, "p50": 9801.2, "median": 9801.2, "p75": 11501.8, "p90": 12459.8, "p95": 13497.6, "p99": 14332.3, "p999": 14917.2}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 5766, "max": 15382, "count": 296, "mean": 9946.1, "p50": 9801.2, "median": 9801.2, "p75": 11501.8, "p90": 12459.8, "p95": 13497.6, "p99": 14332.3, "p999": 14917.2}, "vusers.session_length": {"min": 5828.6, "max": 15437.8, "count": 296, "mean": 10014.5, "p50": 9801.2, "median": 9801.2, "p75": 11501.8, "p90": 12459.8, "p95": 13497.6, "p99": 14332.3, "p999": 14917.2}}, "histograms": {"http.response_time": {"min": 5766, "max": 15382, "count": 296, "mean": 9946.1, "p50": 9801.2, "median": 9801.2, "p75": 11501.8, "p90": 12459.8, "p95": 13497.6, "p99": 14332.3, "p999": 14917.2}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 5766, "max": 15382, "count": 296, "mean": 9946.1, "p50": 9801.2, "median": 9801.2, "p75": 11501.8, "p90": 12459.8, "p95": 13497.6, "p99": 14332.3, "p999": 14917.2}, "vusers.session_length": {"min": 5828.6, "max": 15437.8, "count": 296, "mean": 10014.5, "p50": 9801.2, "median": 9801.2, "p75": 11501.8, "p90": 12459.8, "p95": 13497.6, "p99": 14332.3, "p999": 14917.2}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 233, "vusers.created": 233, "http.requests": 233, "http.codes.200": 280, "http.responses": 280, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 280, "vusers.failed": 0, "vusers.completed": 280}, "rates": {"http.request_rate": 23}, "http.request_rate": null, "firstCounterAt": 1716317920005, "firstHistogramAt": 1716317920010, "lastCounterAt": 1716317929998, "lastHistogramAt": 1716317929998, "firstMetricAt": 1716317920005, "lastMetricAt": 1716317929998, "period": "1716317920000", "summaries": {"http.response_time": {"min": 6020, "max": 15015, "count": 280, "mean": 9940.4, "p50": 9801.2, "median": 9801.2, "p75": 11501.8, "p90": 12459.8, "p95": 12711.5, "p99": 13770.3, "p999": 13770.3}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 6020, "max": 15015, "count": 280, "mean": 9940.4, "p50": 9801.2, "median": 9801.2, "p75": 11501.8, "p90": 12459.8, "p95": 12711.5, "p99": 13770.3, "p999": 13770.3}, "vusers.session_length": {"min": 6096.9, "max": 15091.5, "count": 280, "mean": 10009, "p50": 9999.2, "median": 9999.2, "p75": 11501.8, "p90": 12459.8, "p95": 12968.3, "p99": 13770.3, "p999": 13770.3}}, "histograms": {"http.response_time": {"min": 6020, "max": 15015, "count": 280, "mean": 9940.4, "p50": 9801.2, "median": 9801.2, "p75": 11501.8, "p90": 12459.8, "p95": 12711.5, "p99": 13770.3, "p999": 13770.3}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 6020, "max": 15015, "count": 280, "mean": 9940.4, "p50": 9801.2, "median": 9801.2, "p75": 11501.8, "p90": 12459.8, "p95": 12711.5, "p99": 13770.3, "p999": 13770.3}, "vusers.session_length": {"min": 6096.9, "max": 15091.5, "count": 280, "mean": 10009, "p50": 9999.2, "median": 9999.2, "p75": 11501.8, "p90": 12459.8, "p95": 12968.3, "p99": 13770.3, "p999": 13770.3}}}, {"counters": {"http.codes.200": 329, "http.responses": 329, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 329, "vusers.failed": 0, "vusers.completed": 329, "vusers.created_by_name.Scrape a URL": 100, "vusers.created": 100, "http.requests": 100}, "rates": {"http.request_rate": 10}, "http.request_rate": null, "firstCounterAt": 1716317930008, "firstHistogramAt": 1716317930050, "lastCounterAt": 1716317939983, "lastHistogramAt": 1716317939983, "firstMetricAt": 1716317930008, "lastMetricAt": 1716317939983, "period": "1716317930000", "summaries": {"http.response_time": {"min": 1075, "max": 13638, "count": 329, "mean": 7506.4, "p50": 8520.7, "median": 8520.7, "p75": 10407.3, "p90": 11734.2, "p95": 12213.1, "p99": 13230.3, "p999": 13497.6}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1075, "max": 13638, "count": 329, "mean": 7506.4, "p50": 8520.7, "median": 8520.7, "p75": 10407.3, "p90": 11734.2, "p95": 12213.1, "p99": 13230.3, "p999": 13497.6}, "vusers.session_length": {"min": 1132.9, "max": 13770.4, "count": 329, "mean": 7574.3, "p50": 8520.7, "median": 8520.7, "p75": 10407.3, "p90": 11734.2, "p95": 12213.1, "p99": 13497.6, "p999": 13497.6}}, "histograms": {"http.response_time": {"min": 1075, "max": 13638, "count": 329, "mean": 7506.4, "p50": 8520.7, "median": 8520.7, "p75": 10407.3, "p90": 11734.2, "p95": 12213.1, "p99": 13230.3, "p999": 13497.6}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1075, "max": 13638, "count": 329, "mean": 7506.4, "p50": 8520.7, "median": 8520.7, "p75": 10407.3, "p90": 11734.2, "p95": 12213.1, "p99": 13230.3, "p999": 13497.6}, "vusers.session_length": {"min": 1132.9, "max": 13770.4, "count": 329, "mean": 7574.3, "p50": 8520.7, "median": 8520.7, "p75": 10407.3, "p90": 11734.2, "p95": 12213.1, "p99": 13497.6, "p999": 13497.6}}}, {"counters": {"vusers.created_by_name.Scrape a URL": 100, "vusers.created": 100, "http.requests": 100, "http.codes.200": 101, "http.responses": 101, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 101, "vusers.failed": 0, "vusers.completed": 101}, "rates": {"http.request_rate": 10}, "http.request_rate": null, "firstCounterAt": 1716317940008, "firstHistogramAt": 1716317940093, "lastCounterAt": 1716317949957, "lastHistogramAt": 1716317949937, "firstMetricAt": 1716317940008, "lastMetricAt": 1716317949957, "period": "1716317940000", "summaries": {"http.response_time": {"min": 1026, "max": 1557, "count": 101, "mean": 1226.4, "p50": 1200.1, "median": 1200.1, "p75": 1300.1, "p90": 1380.5, "p95": 1408.4, "p99": 1525.7, "p999": 1525.7}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1026, "max": 1557, "count": 101, "mean": 1226.4, "p50": 1200.1, "median": 1200.1, "p75": 1300.1, "p90": 1380.5, "p95": 1408.4, "p99": 1525.7, "p999": 1525.7}, "vusers.session_length": {"min": 1085.9, "max": 1623.8, "count": 101, "mean": 1293.1, "p50": 1274.3, "median": 1274.3, "p75": 1353.1, "p90": 1436.8, "p95": 1465.9, "p99": 1620, "p999": 1620}}, "histograms": {"http.response_time": {"min": 1026, "max": 1557, "count": 101, "mean": 1226.4, "p50": 1200.1, "median": 1200.1, "p75": 1300.1, "p90": 1380.5, "p95": 1408.4, "p99": 1525.7, "p999": 1525.7}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1026, "max": 1557, "count": 101, "mean": 1226.4, "p50": 1200.1, "median": 1200.1, "p75": 1300.1, "p90": 1380.5, "p95": 1408.4, "p99": 1525.7, "p999": 1525.7}, "vusers.session_length": {"min": 1085.9, "max": 1623.8, "count": 101, "mean": 1293.1, "p50": 1274.3, "median": 1274.3, "p75": 1353.1, "p90": 1436.8, "p95": 1465.9, "p99": 1620, "p999": 1620}}}, {"counters": {"http.codes.200": 101, "http.responses": 101, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 101, "vusers.failed": 0, "vusers.completed": 101, "vusers.created_by_name.Scrape a URL": 100, "vusers.created": 100, "http.requests": 100}, "rates": {"http.request_rate": 10}, "http.request_rate": null, "firstCounterAt": 1716317950008, "firstHistogramAt": 1716317950030, "lastCounterAt": 1716317959999, "lastHistogramAt": 1716317959999, "firstMetricAt": 1716317950008, "lastMetricAt": 1716317959999, "period": "1716317950000", "summaries": {"http.response_time": {"min": 1025, "max": 1596, "count": 101, "mean": 1190.9, "p50": 1176.4, "median": 1176.4, "p75": 1249.1, "p90": 1300.1, "p95": 1380.5, "p99": 1436.8, "p999": 1436.8}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1025, "max": 1596, "count": 101, "mean": 1190.9, "p50": 1176.4, "median": 1176.4, "p75": 1249.1, "p90": 1300.1, "p95": 1380.5, "p99": 1436.8, "p999": 1436.8}, "vusers.session_length": {"min": 1094.2, "max": 1664.5, "count": 101, "mean": 1252.8, "p50": 1249.1, "median": 1249.1, "p75": 1326.4, "p90": 1380.5, "p95": 1436.8, "p99": 1495.5, "p999": 1495.5}}, "histograms": {"http.response_time": {"min": 1025, "max": 1596, "count": 101, "mean": 1190.9, "p50": 1176.4, "median": 1176.4, "p75": 1249.1, "p90": 1300.1, "p95": 1380.5, "p99": 1436.8, "p999": 1436.8}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1025, "max": 1596, "count": 101, "mean": 1190.9, "p50": 1176.4, "median": 1176.4, "p75": 1249.1, "p90": 1300.1, "p95": 1380.5, "p99": 1436.8, "p999": 1436.8}, "vusers.session_length": {"min": 1094.2, "max": 1664.5, "count": 101, "mean": 1252.8, "p50": 1249.1, "median": 1249.1, "p75": 1326.4, "p90": 1380.5, "p95": 1436.8, "p99": 1495.5, "p999": 1495.5}}}, {"counters": {"http.codes.200": 100, "http.responses": 100, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 100, "vusers.failed": 0, "vusers.completed": 100, "vusers.created_by_name.Scrape a URL": 100, "vusers.created": 100, "http.requests": 100}, "rates": {"http.request_rate": 10}, "http.request_rate": null, "firstCounterAt": 1716317960008, "firstHistogramAt": 1716317960016, "lastCounterAt": 1716317969957, "lastHistogramAt": 1716317969923, "firstMetricAt": 1716317960008, "lastMetricAt": 1716317969957, "period": "1716317960000", "summaries": {"http.response_time": {"min": 1020, "max": 1644, "count": 100, "mean": 1226.7, "p50": 1200.1, "median": 1200.1, "p75": 1274.3, "p90": 1380.5, "p95": 1525.7, "p99": 1620, "p999": 1620}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1020, "max": 1644, "count": 100, "mean": 1226.7, "p50": 1200.1, "median": 1200.1, "p75": 1274.3, "p90": 1380.5, "p95": 1525.7, "p99": 1620, "p999": 1620}, "vusers.session_length": {"min": 1079.5, "max": 1732.4, "count": 100, "mean": 1293, "p50": 1274.3, "median": 1274.3, "p75": 1353.1, "p90": 1465.9, "p95": 1587.9, "p99": 1720.2, "p999": 1720.2}}, "histograms": {"http.response_time": {"min": 1020, "max": 1644, "count": 100, "mean": 1226.7, "p50": 1200.1, "median": 1200.1, "p75": 1274.3, "p90": 1380.5, "p95": 1525.7, "p99": 1620, "p999": 1620}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1020, "max": 1644, "count": 100, "mean": 1226.7, "p50": 1200.1, "median": 1200.1, "p75": 1274.3, "p90": 1380.5, "p95": 1525.7, "p99": 1620, "p999": 1620}, "vusers.session_length": {"min": 1079.5, "max": 1732.4, "count": 100, "mean": 1293, "p50": 1274.3, "median": 1274.3, "p75": 1353.1, "p90": 1465.9, "p95": 1587.9, "p99": 1720.2, "p999": 1720.2}}}, {"counters": {"http.codes.200": 99, "http.responses": 99, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 99, "vusers.failed": 0, "vusers.completed": 99, "vusers.created_by_name.Scrape a URL": 100, "vusers.created": 100, "http.requests": 100}, "rates": {"http.request_rate": 10}, "http.request_rate": null, "firstCounterAt": 1716317970008, "firstHistogramAt": 1716317970100, "lastCounterAt": 1716317979997, "lastHistogramAt": 1716317979997, "firstMetricAt": 1716317970008, "lastMetricAt": 1716317979997, "period": "1716317970000", "summaries": {"http.response_time": {"min": 1023, "max": 1480, "count": 99, "mean": 1195.6, "p50": 1176.4, "median": 1176.4, "p75": 1249.1, "p90": 1353.1, "p95": 1436.8, "p99": 1465.9, "p999": 1465.9}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1023, "max": 1480, "count": 99, "mean": 1195.6, "p50": 1176.4, "median": 1176.4, "p75": 1249.1, "p90": 1353.1, "p95": 1436.8, "p99": 1465.9, "p999": 1465.9}, "vusers.session_length": {"min": 1079.2, "max": 1537.6, "count": 99, "mean": 1262.3, "p50": 1249.1, "median": 1249.1, "p75": 1326.4, "p90": 1408.4, "p95": 1495.5, "p99": 1525.7, "p999": 1525.7}}, "histograms": {"http.response_time": {"min": 1023, "max": 1480, "count": 99, "mean": 1195.6, "p50": 1176.4, "median": 1176.4, "p75": 1249.1, "p90": 1353.1, "p95": 1436.8, "p99": 1465.9, "p999": 1465.9}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1023, "max": 1480, "count": 99, "mean": 1195.6, "p50": 1176.4, "median": 1176.4, "p75": 1249.1, "p90": 1353.1, "p95": 1436.8, "p99": 1465.9, "p999": 1465.9}, "vusers.session_length": {"min": 1079.2, "max": 1537.6, "count": 99, "mean": 1262.3, "p50": 1249.1, "median": 1249.1, "p75": 1326.4, "p90": 1408.4, "p95": 1495.5, "p99": 1525.7, "p999": 1525.7}}}, {"counters": {"http.codes.200": 82, "http.responses": 82, "plugins.metrics-by-endpoint./v0/scrape.codes.200": 82, "vusers.failed": 0, "vusers.completed": 82, "vusers.created_by_name.Scrape a URL": 68, "vusers.created": 68, "http.requests": 68}, "rates": {"http.request_rate": 10}, "http.request_rate": null, "firstCounterAt": 1716317980008, "firstHistogramAt": 1716317980076, "lastCounterAt": 1716317987944, "lastHistogramAt": 1716317987944, "firstMetricAt": 1716317980008, "lastMetricAt": 1716317987944, "period": "1716317980000", "summaries": {"http.response_time": {"min": 1025, "max": 1482, "count": 82, "mean": 1192, "p50": 1176.4, "median": 1176.4, "p75": 1249.1, "p90": 1326.4, "p95": 1408.4, "p99": 1465.9, "p999": 1465.9}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1025, "max": 1482, "count": 82, "mean": 1192, "p50": 1176.4, "median": 1176.4, "p75": 1249.1, "p90": 1326.4, "p95": 1408.4, "p99": 1465.9, "p999": 1465.9}, "vusers.session_length": {"min": 1088.6, "max": 1542.2, "count": 82, "mean": 1253.8, "p50": 1224.4, "median": 1224.4, "p75": 1326.4, "p90": 1380.5, "p95": 1465.9, "p99": 1525.7, "p999": 1525.7}}, "histograms": {"http.response_time": {"min": 1025, "max": 1482, "count": 82, "mean": 1192, "p50": 1176.4, "median": 1176.4, "p75": 1249.1, "p90": 1326.4, "p95": 1408.4, "p99": 1465.9, "p999": 1465.9}, "plugins.metrics-by-endpoint.response_time./v0/scrape": {"min": 1025, "max": 1482, "count": 82, "mean": 1192, "p50": 1176.4, "median": 1176.4, "p75": 1249.1, "p90": 1326.4, "p95": 1408.4, "p99": 1465.9, "p999": 1465.9}, "vusers.session_length": {"min": 1088.6, "max": 1542.2, "count": 82, "mean": 1253.8, "p50": 1224.4, "median": 1224.4, "p75": 1326.4, "p90": 1380.5, "p95": 1465.9, "p99": 1525.7, "p999": 1525.7}}}]}