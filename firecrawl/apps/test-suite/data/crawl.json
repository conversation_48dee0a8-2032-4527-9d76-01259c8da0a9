[{"website": "https://www.vellum.ai/llm-leaderboard", "expected_min_num_of_pages": 1, "expected_crawled_pages": ["https://www.vellum.ai/llm-leaderboard"]}, {"website": "https://openai.com/news", "expected_min_num_of_pages": 4, "expected_crawled_pages": ["https://openai.com/news/company/", "https://openai.com/news/research/", "https://openai.com/news/safety-and-alignment/", "https://openai.com/news/stories/"]}, {"website": "https://www.framer.com/pricing", "expected_min_num_of_pages": 1, "expected_not_crawled_pages": ["https://www.framer.com/features/navigation/", "https://www.framer.com/contact/", "https://www.framer.com/add-ons/", "https://www.framer.com/free-saas-ui-kit/", "https://www.framer.com/help/", "https://www.framer.com/features/effects/", "https://www.framer.com/enterprise/", "https://www.framer.com/templates/"]}, {"website": "https://mendable.ai/pricing", "expected_min_num_of_pages": 1, "expected_not_crawled_pages": ["https://mendable.ai/", "https://mendable.ai/blog", "https://mendable.ai/signin", "https://mendable.ai/signup", "https://mendable.ai", "https://mendable.ai/usecases/sales-enablement", "https://mendable.ai/usecases/documentation", "https://mendable.ai/usecases/cs-enablement", "https://mendable.ai/usecases/productcopilot", "https://mendable.ai/security"], "notes": "This one should not go backwards, but it does!"}, {"website": "https://agentops.ai/blog", "expected_min_num_of_pages": 6, "expected_crawled_pages": ["https://www.agentops.ai/blog/effortless-hr-management-with-saas", "https://www.agentops.ai/blog/streamlining-hr-with-saas", "https://www.agentops.ai/blog/simplify-hr-with-modern-saas-solutions", "https://www.agentops.ai/blog/efficient-hr-operations-with-saas", "https://www.agentops.ai/blog/hr-made-simple-with-saas", "https://agentops.ai/blog"], "expected_not_crawled_pages": ["https://agentops.ai/about-us", "https://agentops.ai/contact-us"]}, {"website": "https://en.wikipedia.org/wiki/T._<PERSON><PERSON>_<PERSON>", "expected_min_num_of_pages": 1, "expected_not_crawled_pages": ["https://en.wikipedia.org/wiki/Wikipedia:Contents", "https://en.wikipedia.org/wiki/Wikipedia:Contact_us", "https://en.wikipedia.org/wiki/V._<PERSON><PERSON>_<PERSON>", "https://en.wikipedia.org/wiki/Wikipedia:About", "https://en.wikipedia.org/wiki/Help:Introduction", "https://en.wikipedia.org/wiki/<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>", "https://en.wikipedia.org/wiki/File:<PERSON>.<PERSON><PERSON>_<PERSON><PERSON>_in_1994.jpg"]}, {"website": "https://ycombinator.com/companies", "expected_min_num_of_pages": 20, "expected_crawled_pages": ["https://www.ycombinator.com/companies/industry/elearning", "https://www.ycombinator.com/companies/industry/computer-vision", "https://www.ycombinator.com/companies/industry/health-tech", "https://www.ycombinator.com/companies/industry/education", "https://www.ycombinator.com/companies/industry/robotics", "https://www.ycombinator.com/companies/industry/hardware", "https://www.ycombinator.com/companies/industry/saas", "https://www.ycombinator.com/companies/industry/hard-tech", "https://www.ycombinator.com/companies/industry/developer-tools", "https://www.ycombinator.com/companies/industry/entertainment", "https://www.ycombinator.com/companies/industry/finance", "https://www.ycombinator.com/companies/industry/generative-ai", "https://www.ycombinator.com/companies/industry/machine-learning"]}, {"website": "https://firecrawl.dev", "expected_min_num_of_pages": 2, "expected_crawled_pages": ["https://www.firecrawl.dev/", "https://www.firecrawl.dev/pricing"]}, {"website": "https://fly.io/docs/gpus/gpu-quickstart", "expected_min_num_of_pages": 1, "expected_not_crawled_pages": ["https://fly.io/docs/getting-started/", "https://fly.io/docs/hands-on/", "https://fly.io/docs/about/support/", "https://fly.io/docs/blueprints/going-to-production-with-healthcare-apps/", "https://fly.io/docs/machines/flyctl/fly-machine-update/", "https://fly.io/docs/blueprints/review-apps-guide/", "https://fly.io/docs/blueprints/supercronic/"], "notes": "This one should not go backwards, but it does!"}, {"website": "https://www.instructables.com/circuits", "expected_min_num_of_pages": 12, "expected_crawled_pages": ["https://www.instructables.com/circuits/", "https://www.instructables.com/circuits/apple/projects/", "https://www.instructables.com/circuits/art/projects/", "https://www.instructables.com/circuits/electronics/projects/", "https://www.instructables.com/circuits/microsoft/projects/", "https://www.instructables.com/circuits/microcontrollers/projects/", "https://www.instructables.com/circuits/community/", "https://www.instructables.com/circuits/leds/projects/", "https://www.instructables.com/circuits/gadgets/projects/", "https://www.instructables.com/circuits/arduino/projects/", "https://www.instructables.com/circuits/lasers/projects/", "https://www.instructables.com/circuits/clocks/projects/"]}, {"website": "https://richmondconfidential.org", "expected_min_num_of_pages": 20, "expected_crawled_pages": ["https://richmondconfidential.org/2009/10/13/salesians-star-guard-has-a-big-impact/", "https://richmondconfidential.org/2009/10/13/on-team-of-beginners-oilers-old-hand-stands-out/", "https://richmondconfidential.org/2009/10/19/point-richmond-clockmaker-turns-clutter-into-crafts/", "https://richmondconfidential.org/2009/10/13/profile-maurice-cathy/", "https://richmondconfidential.org/2009/10/13/soul-food-rescue-mission-rebuilds-diets-and-lives/", "https://richmondconfidential.org/2009/10/21/in-tough-economy-pain-trickles-to-the-bottom/", "https://richmondconfidential.org/2009/10/19/richmond-homicide-map/", "https://richmondconfidential.org/2009/10/13/rough-roads-for-richmonds-cab-drivers/", "https://richmondconfidential.org/2009/10/13/before-napa-there-was-winehaven/", "https://richmondconfidential.org/2009/10/13/family-calls-for-end-to-violence-at-memorial-for-slain-woman-friend/"]}]