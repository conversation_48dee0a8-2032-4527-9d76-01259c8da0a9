{"name": "@mendable/firecrawl-js", "version": "1.29.1", "description": "JavaScript SDK for Firecrawl API", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"import": "./dist/index.js", "default": "./dist/index.cjs"}}, "type": "module", "scripts": {"build": "tsup", "build-and-publish": "npm run build && npm publish --access public", "publish-beta": "npm run build && npm publish --access public --tag beta", "test": "NODE_OPTIONS=--experimental-vm-modules jest --verbose src/__tests__/v1/**/*.test.ts"}, "repository": {"type": "git", "url": "git+https://github.com/mendableai/firecrawl.git"}, "author": "Mendable.ai", "license": "MIT", "dependencies": {"typescript-event-target": "^1.1.1", "zod": "^3.23.8", "zod-to-json-schema": "^3.23.0", "axios": "^1.6.8"}, "bugs": {"url": "https://github.com/mendableai/firecrawl/issues"}, "homepage": "https://github.com/mendableai/firecrawl#readme", "devDependencies": {"@jest/globals": "^29.7.0", "@types/axios": "^0.14.0", "@types/dotenv": "^8.2.0", "@types/jest": "^29.5.14", "@types/mocha": "^10.0.6", "@types/node": "^20.12.12", "@types/uuid": "^9.0.8", "dotenv": "^16.4.5", "jest": "^29.7.0", "ts-jest": "^29.2.2", "tsup": "^8.2.4", "typescript": "^5.4.5", "uuid": "^9.0.1"}, "keywords": ["firecrawl", "mendable", "crawler", "web", "scraper", "api", "sdk"], "engines": {"node": ">=22.0.0"}}