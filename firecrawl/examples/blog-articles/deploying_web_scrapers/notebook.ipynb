{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# How to Deploy Python Web Scrapers Online in 2025 For Free"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Introduction"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Web scraping projects may start on your machine, but unless you're willing to ship your laptop to random strangers on the Internet, you'll need cloud services.\n", "\n", "![](images/meme.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There are many compelling reasons to move them to the cloud and make them more reliable. This guide explores several methods for automating and deploying web scrapers in 2025, focusing on free solutions.\n", "\n", "Here is a general outline of concepts we will cover:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- Setting up automated scraping with GitHub Actions\n", "- Deploying to PaaS platforms (Heroku, PythonAnywhere)\n", "- Implementing serverless solutions (AWS Lambda, Google Cloud Functions)\n", "- Container-based deployments with Docker and Kubernetes\n", "- Best practices for monitoring, security, and optimization"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Why Move Web Scrapers to the Cloud?"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The number one reason to deploy scrapers to the cloud is reliability. Cloud-based scrapers run 24/7, without cigarette breaks and the best part, without depending on your local machine. \n", "\n", "Cloud-based scrapers also handle large-scale data operations more easily and gracefully, often juggling multiple scraping tasks. And, if you are a bit more aggressive in your request frequencies and get a dreaded IP ban, cloud services can give access to other IP addresses and geographic locations. \n", "\n", "Moreover, you are not limited by your laptop's specs because cloud gives you dedicated resources. While these resources may gouge a hole in your pocket for large-scale scraping operations, many platforms offer generous free tiers, as we will explore soon."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## How to Choose the Right Deployment Method For Your Scrapers"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We are about to list 6-7 deployment methods in this article, so you might get a decision fatigue. To prevent that, give this section a cursory read as it helps you choose the right one based on your scale requirements, technical complexity, and budget. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Scale requirements"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In this section, we'll dive into three deployment tiers that match your scale - from lightweight solutions to heavy-duty scraping powerhouses. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Small scale (1-1000 requests/day)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- **Best Options**: \n", "  - GitHub Actions\n", "  - PythonAnywhere\n", "  - <PERSON><PERSON> (Free Tier)\n", "- **Why**: These platforms offer sufficient resources for basic scraping needs without cost\n", "- **Limitations**: Daily request caps and runtime restrictions\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Medium Scale (1000-10000 requests/day)\n", "- **Best Options**:\n", "  - AWS Lambda\n", "  - Google Cloud Functions\n", "  - Docker containers on basic VPS\n", "- **Why**: Better handling of concurrent requests and flexible scaling\n", "- **Considerations**: Cost begins to factor in, but still manageable"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Large Scale (10000+ requests/day)\n", "- **Best Options**:\n", "  - Kubernetes clusters\n", "  - Multi-region serverless deployments\n", "  - Specialized scraping platforms\n", "- **Why**: Robust infrastructure for high-volume operations\n", "- **Trade-offs**: Higher complexity and cost vs. reliability"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Technical complexity"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, let's categorize the methods based on how fast you can get them up and running. \n", "\n", "#### Low Complexity Solutions\n", "- **GitHub Actions**\n", "  - Pros: Simple setup, version control integration\n", "  - Cons: Limited customization\n", "- **PythonAnywhere**\n", "  - Pros: User-friendly interface\n", "  - Cons: Resource constraints"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "#### Medium Complexity Solutions\n", "- **Serverless (AWS Lambda/Google Functions)**\n", "  - Pros: Managed infrastructure, auto-scaling\n", "  - Cons: Learning curve for configuration\n", "- **Docker Containers**\n", "  - Pros: Consistent environments\n", "  - Cons: Container management overhead"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "#### High Complexity Solutions\n", "- **Kubernetes**\n", "  - Pros: Ultimate flexibility and scalability\n", "  - Cons: Significant operational overhead\n", "- **Custom Infrastructure**\n", "  - Pros: Complete control\n", "  - Cons: Requires DevOps expertise"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Budget Considerations"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In terms of cost, all methods in the article has the following generous free tiers or next-to-nothing cheap starting plans:\n", "\n", "- GitHub Actions: 2000 minutes/month\n", "- Heroku: $5 and up\n", "- AWS Lambda: starting at 0.2$ per 1 million requests\n", "- Google Cloud Functions: 2 million invocations/month"]}, {"cell_type": "markdown", "metadata": {}, "source": ["These limits are based on various cost factors like compute time, data transfer speeds, storage requirements and additional services like databases or monitoring.\n", "\n", "Here is a little decision matrix to distill all this information:\n", "\n", "| Factor          | Small Project | Medium Project | Large Project |\n", "|-----------------|---------------|----------------|---------------|\n", "| Best Platform   | GitHub Actions| AWS Lambda     | Kubernetes   |\n", "| Monthly Cost    | $0           | $10-50         | $100+        |\n", "| Setup Time      | 1-2 hours    | 1-2 days       | 1-2 weeks    |\n", "| Maintenance     | Minimal      | Moderate       | Significant  |\n", "| Scalability     | Limited      | Good           | Excellent    |\n", "\n", "-------------"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Start small with simpler platforms and gather data on your scraping needs by measuring actual usage patterns. From there, you can gradually scale and potentially implement hybrid approaches that balance complexity and benefits."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Prerequisites"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This article assumes familiarity with web scraping fundamentals like HTML parsing, CSS selectors, HTTP requests, and handling dynamic content. You should also be comfortable with Python basics including functions, loops, and working with external libraries. Basic knowledge of command line tools and git version control will be essential for deployment."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Required accounts"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Before starting with any deployment method, you'll need to create accounts on these platforms:\n", "\n", "1. **[GitHub account](github.com)** (Required)\n", "   - Needed for version control and GitHub Actions\n", "\n", "2. **Cloud Platform Account** (<PERSON>ose at least one)\n", "   - [AWS account](https://signin.aws.amazon.com/signup?request_type=register) (for Lambda)\n", "   - [Google Cloud account](https://cloud.google.com/free) (for Cloud Functions)\n", "   - [Heroku account](https://signup.heroku.com/)\n", "   - [PythonAnywhere account](https://www.pythonanywhere.com/)\n", "\n", "3. **[Docker Hub account](https://hub.docker.com/)** (Optional)\n", "   - Only needed if using container-based deployments\n", "   - Required for storing and sharing Docker images\n", "\n", "4. **[Firecrawl account](https://firecrawl.dev)** (Optional)\n", "   - Only needed if you decide to use an AI-based scraper (more on Firecrawl soon).  \n", "\n", "Note: Most cloud platforms require a credit card for verification, even when using free tiers. However, they won't charge you unless you exceed free tier limits."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Building a basic scraper"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To demonstrate deployment concepts effectively, we'll start by building a basic web scraper using Firecrawl, a modern scraping API that simplifies many common challenges.\n", "\n", "[Firecrawl](https://docs.firecrawl.dev) offers several key advantages compared to traditional Python web scraping libraries:\n", "\n", "- Dead simple to use with only a few dependencies\n", "- Handles complex scraping challenges automatically (proxies, anti-bot mechanisms, dynamic JS content)\n", "- Converts web content into clean, LLM-ready markdown format\n", "- Supports multiple output formats (markdown, structured data, screenshots, HTML)\n", "- Reliable extraction with retry mechanisms and error handling\n", "- Supports custom actions (click, scroll, input, wait) before data extraction\n", "- Geographic location customization for avoiding IP bans\n", "- Built-in rate limiting and request management\n", "\n"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "plaintext"}}, "source": ["As an example, we will build a simple scraper for [ProductHunt](https://www.producthunt.com/). Specifically, we will scrape the \"Yesterday's Top Products\" list from the homepage:"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "plaintext"}}, "source": ["![](images/ph-homepage.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The scraper extracts the following information from each product:"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "plaintext"}}, "source": ["![](images/ph-sample.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's get building:\n", "\n", "```bash\n", "$ mkdir product-hunt-scraper\n", "$ cd product-hunt-scraper\n", "$ touch scraper.py .env\n", "$ python -m venv venv\n", "$ source venv/bin/activate\n", "$ pip install pydantic firecrawl-py\n", "$ echo \"FIRECRAWL_API_KEY='your-api-key-here' >> .env\"\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["These commands set up a working directory for the scraper, along with a virtual environment and the main script. The last part also saves your Firecrawl API key, which you can get through their free plan by [signing up for an account](firecrawl.dev).\n", "\n", "\n", "Let's work on the code now:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "from firecrawl import FirecrawlApp\n", "from dotenv import load_dotenv\n", "from pydantic import BaseModel, Field\n", "from datetime import datetime\n", "\n", "load_dotenv()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["First, we import a few packages and the `FirecrawlApp` class - we use it to establish a connection with Firecrawl's scraping engine. Then, we define a Pydantic class outlining the details we want to scrape from each product:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["class Product(BaseModel):\n", "    name: str = Field(description=\"The name of the product\")\n", "    description: str = Field(description=\"A short description of the product\")\n", "    url: str = Field(description=\"The URL of the product\")\n", "    \n", "    topics: list[str] = Field(\n", "        description=\"A list of topics the product belongs to. Can be found below the product description.\"\n", "    )\n", "    \n", "    n_upvotes: int = Field(description=\"The number of upvotes the product has\")\n", "    n_comments: int = Field(description=\"The number of comments the product has\")\n", "    \n", "    rank: int = Field(\n", "        description=\"The rank of the product on Product Hunt's Yesterday's Top Products section.\"\n", "    )\n", "    logo_url: str = Field(description=\"The URL of the product's logo.\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The field descriptions in this class play a crucial role in guiding the LLM scraping engine. By providing natural language descriptions for each field, we tell the LLM exactly what information to look for and where to find it on the page. For example, when we say \"A list of topics under the product description\", the LLM understands both the content we want (topics) and its location (below the description). \n", "\n", "This natural language approach allows Firecrawl to intelligently parse the page's HTML structure and extract the right information without requiring explicit CSS selectors or XPaths. The LLM analyzes the semantic meaning of our descriptions and matches them to the appropriate elements on the page.\n", "\n", "This approach offers two practical advantages: it reduces initial development time since you don't need to manually inspect HTML structures, and it provides long-lasting resilience against HTML changes. Since the LLM understands the semantic meaning of the elements rather than relying on specific selectors, it can often continue working even when class names or IDs are updated. This makes it suitable for scenarios where long-term maintenance is a consideration."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Getting back to code, we write another Pydantic class for scraping a collection of Products from the 'Yesterday's Top Products' list:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["class YesterdayTopProducts(BaseModel):\n", "    products: list[Product] = Field(\n", "        description=\"A list of top products from yesterday on Product Hunt.\"\n", "    )"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The `YesterdayTopProducts` parent class is essential - without it, Firecrawl would only scrape a single product instead of the full list. This happens because Firecrawl strictly adheres to the provided schema structure, ensuring consistent and reliable output on every scraping run."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, we define a function that scrapes ProductHunt based on the schema we just define:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["BASE_URL = \"https://www.producthunt.com\"\n", "\n", "\n", "def get_yesterday_top_products():\n", "    app = FirecrawlApp()\n", "\n", "    data = app.scrape_url(\n", "        BASE_URL,\n", "        params={\n", "            \"formats\": [\"extract\"],\n", "            \"extract\": {\n", "                \"schema\": YesterdayTopProducts.model_json_schema(),\n", "                \n", "                \"prompt\": \"Extract the top products listed under the 'Yesterday's Top Products' section. There will be exactly 5 products.\",\n", "            },\n", "        },\n", "    )\n", "\n", "    return data[\"extract\"][\"products\"]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This function initializes a Firecrawl app, which reads your Firecrawl API key stored in an `.env` file and scrapes the URL. Notice the parameters being passed to the `scrape_url()` method:\n", "\n", "- `formats` specifies how the data should be scraped and extracted. Firecrawl supports other formats like markdown, HTML, screenshots or links.\n", "- `schema`: The JSON schema produced by the Pydantic class\n", "- `prompt`: A general prompt guiding the underlying LLM on what to do. Providing a prompt usually improves the performance.\n", "\n", "In the end, the function returns the extracted products, which will be a list of dictionaries. \n", "\n", "The final step is writing a function to save this data to a JSON file:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def save_yesterday_top_products():\n", "    products = get_yesterday_top_products()\n", "    \n", "    date_str = datetime.now().strftime(\"%Y_%m_%d\")\n", "    filename = f\"ph_top_products_{date_str}.json\"\n", "    \n", "    with open(filename, \"w\") as f:\n", "        json.dump(products, f)\n", "        \n", "if __name__ == \"__main__\":\n", "    save_yesterday_top_products()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This function runs the previous one and saves the returned data to a JSON file identifiable with the following day's date. \n", "\n", "----"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We've just built a scraper that resiliently extracts data from ProductHunt. To keep things simple, we implemented everything in a single script with straightforward data persistence. In production environments with larger data flows and more complex websites, your project would likely span multiple directories and files.\n", "\n", "Nevertheless, the deployment methods we'll explore work for nearly any type of project. You can always restructure your own projects to follow this pattern of having a single entry point that coordinates all the intermediate scraping stages."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Deploying Web Scrapers With GitHub Actions"]}, {"cell_type": "markdown", "metadata": {}, "source": ["GitHub Actions is a powerful CI/CD platform built into GitHub that allows you to automate workflows, including running scrapers on a schedule. It provides a simple way to deploy and run automated tasks without managing infrastructure, making it an ideal choice for web scraping projects.\n", "\n", "To get started, initialize Git and commit the work we've completed so far in your working directory:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["```python\n", "$ git init\n", "$ touch .gitignore\n", "$ echo \".env\" >> .gitignore  # Remove the .env file from Git indexing\n", "$ git add .\n", "$ git commit -m \"Initial commit\"\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Then, create an empty GitHub repository, copy its link and set it as the remote for your local repo:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["```bash\n", "$ git remote add origin your-repo-link\n", "$ git push\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Creating workflow files"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Workflow files are YAML configuration files that tell GitHub Actions how to automate tasks in your repository. For our web scraping project, these files will define when and how to run our scraper.\n", "\n", "These files live in the `.github/workflows` directory of your repository and contain instructions for:\n", "- When to trigger the workflow (like on a schedule or when code is pushed)\n", "- What environment to use (Python version, dependencies to install)\n", "- The actual commands to run your scraper\n", "- What to do with the scraped data\n", "\n", "Each workflow file acts like a recipe that GitHub Actions follows to execute your scraper automatically. This automation is perfect for web scraping since we often want to collect data on a regular schedule without manual intervention.\n", "\n", "For our scraper, we need a single workflow file that executes the `scraper.py` file. Let's set it up:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["```bash\n", "$ mkdir -p .github/workflows\n", "$ touch .github/workflows/ph-scraper.yml\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Open the newly-created file and paste the following contents:\n", "\n", "\n", "```yml\n", "name: <PERSON>er\n", "\n", "on:\n", "  schedule:\n", "    - cron: '0 1 * * *'  # Runs at 1 AM UTC daily\n", "  workflow_dispatch:  # Allows manual trigger\n", "\n", "permissions:\n", "  contents: write\n", "\n", "jobs:\n", "  scrape:\n", "    runs-on: ubuntu-latest\n", "    \n", "    steps:\n", "    - name: Checkout repository\n", "      uses: actions/checkout@v3\n", "      with:\n", "          persist-credentials: true\n", "      \n", "    - name: Set up Python\n", "      uses: actions/setup-python@v4\n", "      with:\n", "        python-version: '3.10'\n", "        \n", "    - name: Install dependencies\n", "      run: |\n", "        python -m pip install --upgrade pip\n", "        pip install -r requirements.txt\n", "        \n", "    - name: <PERSON> scraper\n", "      env:\n", "        FIRECRAWL_API_KEY: ${{ secrets.FIRECRAWL_API_KEY }}\n", "      run: python scraper.py\n", "        \n", "    - name: Commit and push if changes\n", "      run: |\n", "        git config --local user.email \"github-actions[bot]@users.noreply.github.com\"\n", "        git config --local user.name \"github-actions[bot]\"\n", "        git add *.json\n", "        git diff --quiet && git diff --staged --quiet || git commit -m \"Update ProductHunt data [skip ci]\"\n", "        git push\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The YAML file defines a GitHub Actions workflow for automated web scraping:\n", "\n", "name: Specifies the workflow name that appears in GitHub Actions UI\n", "\n", "on: Defines how workflow triggers:\n", "- schedule: Uses cron syntax to run daily at 1 AM UTC\n", "- workflow_dispatch: Enables manual workflow triggering\n", "\n", "jobs: Contains the workflow jobs:\n", "- scrape: Main job that runs on ubuntu-latest\n", "  - steps: Sequential actions to execute:\n", "    1. Checkout repository using `actions/checkout`\n", "    2. Setup Python 3.10 environment\n", "    3. Install project dependencies from `requirements.txt`\n", "    4. Run the scraper with environment variables\n", "    5. Commit and push any changes to the repository\n", "\n", "The workflow automatically handles repository interaction, dependency management, and data updates while providing both scheduled and manual execution options. You can read the [official GitHub guide on workflow file syntax](https://docs.github.com/en/actions/writing-workflows/workflow-syntax-for-github-actions#about-yaml-syntax-for-workflows) to learn more.\n", "\n", "For this workflow file to sun successfully, we need to take a couple of additional steps."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Generating a `requirements.txt` file"]}, {"cell_type": "markdown", "metadata": {}, "source": ["One of the steps in the workflow file is installing the dependencies for our project using a `requirements.txt` file, which is a standard format for listing packages used in your project. \n", "\n", "For simple projects, you can create this file manually and adding each package on a new line like:\n", "\n", "```text\n", "pydantic\n", "firecrawl-py\n", "```\n", "\n", "However, if you have a large project with multiple files and dozens of dependencies, you need an automated method. The simplest one I can suggest is using `pipreqs` package:\n", "\n", "```bash\n", "$ pip install pipreqs\n", "$ pipreqs . \n", "```\n", "\n", "`pipreqs` is a lightweight package that scans all Python scripts in your project and adds them to a new `requirements.txt` file with their used versions. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Storing secrets"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you notice, the workflow file has a step that executes `scraper.py`:\n", "\n", "```yml\n", "- name: <PERSON> scraper\n", "      env:\n", "        FIRECRAWL_API_KEY: ${{ secrets.FIRECRAWL_API_KEY }}\n", "      run: python scraper.py\n", "```\n", "\n", "The workflow retrieves environment variables using the `secrets.SECRET_NAME` syntax. Since the `.env` file containing your Firecrawl API key isn't uploaded to GitHub for security reasons, you'll need to store the key in your GitHub repository secrets.\n", "\n", "To add your API key as a secret:\n", "\n", "1. Navigate to your GitHub repository\n", "2. <PERSON><PERSON> on \"<PERSON>ting<PERSON>\" \n", "3. Select \"Secrets and variables\" then \"Actions\"\n", "4. <PERSON><PERSON> \"New repository secret\"\n", "5. Enter \"FIRECRAWL_API_KEY\" as the name\n", "6. Paste your API key as the value\n", "7. <PERSON><PERSON> \"Add secret\"\n", "\n", "This allows the workflow to securely access your API key during execution without exposing it in the repository."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Running the workflow"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Before running the workflow, we need to commit all new changes and push them to GitHub:\n", "\n", "```python\n", "$ git add .\n", "$ git commit -m \"Descriptive commit message\"\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This makes our workflow visible to GitHub.\n", "\n", "At the top of the workflow file, we set a schedule for the workflow file to run at 1 AM using `cron` syntax:\n", "\n", "```yaml\n", "on:\n", "  schedule:\n", "    - cron: '0 1 * * *'  # Runs at 1 AM UTC daily\n", "  workflow_dispatch:  # Allows manual trigger\n", "```\n", "\n", "The `cron` syntax consists of 5 fields representing minute (0-59), hour (0-23), day of month (1-31), month (1-12), and day of week (0-6, where 0 is Sunday). Each field can contain specific values, ranges (1-5), lists (1,3,5), or asterisks (*) meaning \"every\". For example, `0 1 * * *` means \"at minute 0 of hour 1 (1 AM UTC) on every day of every month\". Here are some more patterns:\n", "\n", "- `0 */2 * * *`: Every 2 hours\n", "- `0 9-17 * * 1-5`: Every hour from 9 AM to 5 PM on weekdays\n", "- `*/15 * * * *`: Every 15 minutes\n", "- `0 0 * * 0`: Every Sunday at midnight\n", "- `0 0 1 * *`: First day of every month at midnight\n", "- `30 18 * * 1,3,5`: Monday, Wednesday, Friday at 6:30 PM\n", "\n", "So, once the workflow file is pushed to GitHub, the scraper is scheduler to run. However, the `workflow_dispatch` parameter in the file allows us to run the scraper manually for debugging.\n", "\n", "![](images/workflow.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Navigate to the Actions tab of your GitHub repository, click on the workflow name and press \"Run workflow\". In about a minute (if the workflow is successful), you will see the top five products from yesterday on ProductHunt saved as a JSON file to your repository."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Whenever you want to interrupt the scraping schedule, click on the three buttons in the top-right corner of the workflow page and disable it."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Deploying Web Scrapers With Heroku"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[Heroku](heroku.com) is a Platform-as-a-Service (PaaS) that makes deploying applications straightforward, even for beginners. While it removed its generous free tier in 2022, its basic $5 \"dyno\" plan still has some free features we can take advantage of for the purposes of this tutorial. \n", "\n", "### Setting up <PERSON><PERSON>\n", "\n", "First, install the Heroku CLI and login to your account:\n", "\n", "```bash\n", "$ brew install heroku/brew/heroku  # macOS\n", "$ curl https://cli-assets.heroku.com/install.sh | sh  # Linux\n", "$ heroku login  # Opens your web browser\n", "```\n", "\n", "Then, create a new Heroku app and set it as a remote for your repository:\n", "\n", "```bash\n", "$ heroku create ph-scraper-your-name  # Make the app name unique\n", "$ heroku git:remote -a ph-scraper-your-name\n", "```\n", "\n", "After this step, if you visit [dashboard.heroku.com](dashboard.heroku.com), your app must be visible. \n", "\n", "### Configuring the Application\n", "\n", "Heroku requires a few additional files to run your application. First, create a `Procfile` that tells Hero<PERSON> what command to run:\n", "\n", "```bash\n", "$ touch Procfile\n", "$ echo \"worker: python scraper.py\" > Procfile\n", "```\n", "\n", "Next, create a `runtime.txt` to specify the Python version:\n", "\n", "```bash\n", "$ touch runtime.txt\n", "$ echo \"python-3.10.12\" > runtime.txt\n", "```\n", "\n", "### Environment Variables\n", "\n", "Instead of using a `.env` file, Heroku requires you to set your environment variables directly using the Heroku CLI:\n", "\n", "```bash\n", "$ heroku config:set FIRECRAWL_API_KEY='your-api-key-here'\n", "```\n", "\n", "You can verify the variables are set correctly with:\n", "\n", "```bash\n", "$ heroku config\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Scheduling Scraper Runs\n", "\n", "Hero<PERSON> uses an add-on called [\"Scheduler\"](https://elements.heroku.com/addons/scheduler) for running periodic tasks. Install it with:\n", "\n", "```bash\n", "$ heroku addons:create scheduler:standard\n", "```\n", "\n", "Then open the scheduler dashboard:\n", "\n", "```bash\n", "$ heroku addons:open scheduler\n", "```\n", "\n", "In the web interface, add a new job with the command `python scraper.py` and set your desired frequency (daily, hourly, or every 10 minutes).\n", "\n", "![](images/heroku-scheduler.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Deployment and Monitoring\n", "\n", "Now, to launch everything, you need to deploy your application by committing and pushing the local changes to Heroku:\n", "\n", "```bash\n", "$ git add .\n", "$ git commit -m \"Add Heroku-related files\"\n", "$ git push heroku main\n", "```\n", "\n", "You can periodically monitor the health of your application with the following command:\n", "\n", "```bash\n", "$ heroku logs --tail\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Platform Limitations\n", "\n", "The basic $5 dyno has some important limitations to consider:\n", "\n", "- Sleeps after 30 minutes of inactivity\n", "- Limited to 512MB RAM\n", "- Shares CPU with other applications\n", "- Maximum of 23 hours active time per day\n", "\n", "For most small to medium scraping projects, these limitations aren't problematic. However, if you need more resources, you can upgrade to Standard ($25/month) or Performance ($250/month) dynos."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Data Persistence\n", "\n", "Since <PERSON><PERSON>'s filesystem is temporary, you'll need to modify the scraper to store data externally. Here's a quick example using AWS S3:\n", "\n", "```python\n", "import boto3  # pip install boto3\n", "from datetime import datetime\n", "\n", "def save_yesterday_top_products():\n", "    products = get_yesterday_top_products()\n", "    \n", "    # Initialize S3 client\n", "    s3 = boto3.client('s3')\n", "    \n", "    # Create filename with date\n", "    date_str = datetime.now().strftime(\"%Y_%m_%d\")\n", "    filename = f\"ph_top_products_{date_str}.json\"\n", "    \n", "    # Upload to S3\n", "    s3.put_object(\n", "        Bucket='your-bucket-name',\n", "        Key=filename,\n", "        Body=json.dumps(products)\n", "    )\n", "```\n", "\n", "For this to work, you must already have an AWS account and an existing S3 bucket. Also, you must set your AWS credentials as Heroku secrets through the Heroku CLI:\n", "\n", "```bash\n", "$ heroku config:set AWS_ACCESS_KEY_ID='your-key'\n", "$ heroku config:set AWS_SECRET_ACCESS_KEY='your-secret'\n", "```\n", "\n", "Once you do, add `boto3` to the list of dependencies in your `requirements.txt` file:\n", "\n", "```\n", "$ echo \"boto3\" >> requirements.txt\n", "```\n", "\n", "Finally, commit and push the changes:\n", "\n", "```bash\n", "$ git add .\n", "$ git commit -m \"Switch data persistence to S3\"\n", "$ git push heroku main\n", "$ git push origin main\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can confirm that the app is functioning properly by setting the schedule frequency to 10 minutes and checking your S3 bucket for the JSON file containing the top five products from ProductHunt."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Stopping <PERSON><PERSON> Apps"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To stop your app, you can use a few different methods:\n", "\n", "1. Pause the dyno:\n", "\n", "```bash\n", "$ heroku ps:scale worker=0\n", "```\n", "\n", "This stops the worker dyno without deleting the app. To resume later:\n", "\n", "```bash\n", "$ heroku ps:scale worker=1\n", "```\n", "\n", "2. Disable the scheduler:\n", "\n", "```bash\n", "$ heroku addons:destroy scheduler\n", "```\n", "\n", "Or visit the Heroku dashboard and remove the scheduler add-on manually.\n", "\n", "3. Delete the entire app:\n", "\n", "```bash\n", "$ heroku apps:destroy --app your-app-name --confirm your-app-name\n", "```\n", "⚠️ Warning: This permanently deletes your app and all its data.\n", "\n", "4. Maintenance mode\n", "\n", "```bash\n", "$ heroku maintenance:on\n", "```\n", "\n", "This puts the app in maintenance mode. To disable:\n", "\n", "```bash\n", "$ heroku maintenance:off\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["-------\n", "\n", "To learn more about Heroku and how to run Python applications on its servers, please refer to [their Python support documentation](https://devcenter.heroku.com/categories/python-support)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Deploying Web Scrapers With PythonAnywhere"]}, {"cell_type": "markdown", "metadata": {}, "source": ["[PythonAnywhere](https://www.pythonanywhere.com/) is a cloud-based Python development environment that offers an excellent platform for hosting web scrapers. It provides a free tier that includes:\n", "\n", "- Daily scheduled tasks\n", "- Web-based console access\n", "- 512MB storage\n", "- Basic CPU and memory allocation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Setting Up PythonAnywhere\n", "\n", "First, create a free account at [pythonanywhere.com](https://www.pythonanywhere.com). Once logged in, follow these steps:\n", "\n", "Open a Bash console from your PythonAnywhere dashboard and execute these commands to clone the GitHub repository we've been building:\n", "```bash\n", "\n", "$ git clone https://github.com/your-username/your-repo.git\n", "$ cd your-repo\n", "$ python3 -m venv venv\n", "$ source venv/bin/activate\n", "$ pip install -r requirements.txt\n", "\n", "# Recreate your .env file\n", "$ touch .env\n", "$ echo \"FIRECRAWL_API_KEY='your-api-key-here'\" >> .env\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Scheduling the Scraper"]}, {"cell_type": "markdown", "metadata": {}, "source": ["PA free tier includes a scheduler with a daily frequency. To enable it, follow these steps:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["1. Go to the \"Tasks\" tab in your PythonAnywhere dashboard accessible via https://www.pythonanywhere.com/user/your-username/\n", "2. Add a new scheduled task.\n", "3. Set the timing using the provided interface.\n", "4. Enter the command to run your scraper:\n", "\n", "```bash\n", "cd /home/<USER>/your-repo && source venv/bin/activate && python scraper.py\n", "```\n", "\n", "The command changes the working directory to the project location, activates the virtual environment and executes the scraper. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["![](images/pa-scheduler.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Data Storage Options\n", "\n", "PythonAnywhere's filesystem is persistent, unlike Heroku, so you can store JSON files directly. However, for better scalability, consider using cloud storage:"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "```python\n", "def save_yesterday_top_products():\n", "    \"\"\"\n", "    Change back to JSON-based storage.\n", "    \"\"\"\n", "    products = get_yesterday_top_products()\n", "    \n", "    # Local storage (works on PythonAnywhere)\n", "    date_str = datetime.now().strftime(\"%Y_%m_%d\")\n", "    filename = f\"data/ph_top_products_{date_str}.json\"\n", "    \n", "    # Create data directory if it doesn't exist\n", "    os.makedirs(\"data\", exist_ok=True)\n", "    \n", "    with open(filename, \"w\") as f:\n", "        json.dump(products, f)\n", "```\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Platform benefits & limitations\n", "\n", "PythonAnywhere offers several advantages for web scraping:\n", "\n", "- **Always-on environment**: Unlike Heroku's free tier, PythonAnywhere doesn't sleep\n", "- **Persistent storage**: Files remain stored between runs\n", "- **Simple interface**: User-friendly web console and file editor\n", "- **Built-in scheduler**: No need for additional add-ons\n", "- **Free SSL**: HTTPS requests work out of the box\n", "- **Multiple Python Versions**: Support for different Python versions\n", "\n", "The free tier has some restrictions:\n", "\n", "- Limited to 1 daily task\n", "- CPU/RAM throttling\n", "- 512MB storage limit"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Stopping or Modifying Tasks\n", "\n", "To manage your scraper:\n", "\n", "1. **Pause**: Disable the scheduled task in the Tasks tab\n", "2. **Modify schedule**: Edit timing in the Tasks interface\n", "3. **Delete**: Remove the task completely\n", "4. **Update code**: Pull latest changes from git repository from any PythonAnywhere bash console:\n", "\n", "```bash\n", "$ cd your-repo\n", "$ git pull origin main\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Best Practices and Optimization"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Our scraper and deployment methods are far from perfect. In this section, we will cover some best practices and tips to optimize its performance."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Error Handling & Monitoring\n", "\n", "Proper error handling and monitoring are crucial for maintaining a reliable web scraper. Below, we will implement a few mechanisms."]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "#### Implement a robust retry mechanism:\n", "\n", "```python\n", "from tenacity import retry, stop_after_attempt, wait_exponential\n", "\n", "@retry(\n", "    stop=stop_after_attempt(3),\n", "    wait=wait_exponential(multiplier=1, min=4, max=10),\n", "    reraise=True\n", ")\n", "def get_yesterday_top_products():\n", "    try:\n", "        app = FirecrawlApp()\n", "        data = app.scrape_url(\n", "            BASE_URL,\n", "            params={\n", "                \"formats\": [\"extract\"],\n", "                \"extract\": {\n", "                    \"schema\": YesterdayTopProducts.model_json_schema(),\n", "                    \"prompt\": \"Extract the top products listed under the 'Yesterday's Top Products' section.\"\n", "                },\n", "            },\n", "        )\n", "        return data[\"extract\"][\"products\"]\n", "    except Exception as e:\n", "        logger.error(f\"Scraping failed: {str(e)}\")\n", "        raise\n", "```\n", "\n", "Above, we are implementing a retry mechanism using the tenacity library. It will retry the scraping operation up to 3 times with exponential backoff between attempts. The wait time starts at 4 seconds and increases exponentially up to 10 seconds between retries. If all retries fail, it will raise the last exception. Any errors are logged before being re-raised to trigger the retry mechanism."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Implement comprehensive logging\n", "\n", "```python\n", "import logging\n", "from datetime import datetime\n", "\n", "def setup_logging():\n", "    \"\"\"Configure logging with both file and console handlers.\"\"\"\n", "    logger = logging.getLogger(__name__)\n", "    logger.setLevel(logging.INFO)\n", "\n", "    # Create formatters\n", "    detailed_formatter = logging.Formatter(\n", "        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'\n", "    )\n", "    simple_formatter = logging.Formatter('%(levelname)s: %(message)s')\n", "\n", "    # File handler\n", "    file_handler = logging.FileHandler(\n", "        f'logs/scraper_{datetime.now().strftime(\"%Y%m%d\")}.log'\n", "    )\n", "    file_handler.setFormatter(detailed_formatter)\n", "    \n", "    # Console handler\n", "    console_handler = logging.StreamHandler()\n", "    console_handler.setFormatter(simple_formatter)\n", "\n", "    # Add handlers\n", "    logger.addHandler(file_handler)\n", "    logger.addHandler(console_handler)\n", "\n", "    return logger\n", "\n", "logger = setup_logging()\n", "```\n", "\n", "The logging setup above configures comprehensive logging for our web scraper, which is essential for monitoring, debugging and maintaining the scraper in production. It creates two logging handlers - one that writes detailed logs to dated files (including timestamps and log levels), and another that outputs simplified logs to the console. This dual logging approach helps us track scraper execution both in real-time via console output and historically through log files. Having proper logging is crucial for diagnosing issues, monitoring performance, and ensuring the reliability of our web scraping system."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Set up monitoring alerts\n", "\n", "```python\n", "import requests\n", "\n", "def send_alert(message, webhook_url):\n", "    \"\"\"Send alerts to <PERSON>lack/Discord/etc.\"\"\"\n", "    payload = {\"text\": message}\n", "    try:\n", "        requests.post(webhook_url, json=payload)\n", "    except Exception as e:\n", "        logger.error(f\"Failed to send alert: {str(e)}\")\n", "\n", "def monitor_scraping_health(products):\n", "    \"\"\"Monitor scraping health and send alerts if needed.\"\"\"\n", "    if not products:\n", "        send_alert(\n", "            \"⚠️ Warning: No products scraped from ProductHunt\",\n", "            os.getenv(\"WEBHOOK_URL\")\n", "        )\n", "        return False\n", "        \n", "    if len(products) < 5:\n", "        send_alert(\n", "            f\"⚠️ Warning: Only {len(products)} products scraped (expected 5)\",\n", "            os.getenv(\"WEBHOOK_URL\")\n", "        )\n", "        return False\n", "        \n", "    return True\n", "```\n", "\n", "The monitoring functions above help ensure our scraper is working properly. The `send_alert()` function sends notifications to messaging platforms like Slack or Discord when issues occur, requiring a webhook URL configured in environment variables. The `monitor_scraping_health()` function checks if we're getting the expected amount of scraped data and triggers alerts if not. Learn more about setting up webhooks in [Discord](https://discord.com/developers/docs/resources/webhook) and [Slack](https://api.slack.com/messaging/webhooks)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Data Management\n", "\n", "Proper data management is crucial for a production web scraper. This includes validating the scraped data to ensure quality and consistency, as well as implementing efficient storage mechanisms to handle large volumes of data. Let's look at the key components."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Implement data validation"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "```python\n", "from typing import Optional\n", "from datetime import datetime\n", "\n", "class ProductValidator:\n", "    @staticmethod\n", "    def validate_product(product: dict) -> Optional[str]:\n", "        \"\"\"Validate product data and return error message if invalid.\"\"\"\n", "        required_fields = ['name', 'description', 'url', 'topics']\n", "        \n", "        for field in required_fields:\n", "            if not product.get(field):\n", "                return f\"Missing required field: {field}\"\n", "                \n", "        if not isinstance(product.get('n_upvotes'), int):\n", "            return \"Invalid upvote count\"\n", "            \n", "        if not product.get('url').startswith('http'):\n", "            return \"Invalid URL format\"\n", "            \n", "        return None\n", "\n", "def validate_products(products: list) -> list:\n", "    \"\"\"Validate and filter products.\"\"\"\n", "    valid_products = []\n", "    \n", "    for product in products:\n", "        error = ProductValidator.validate_product(product)\n", "        if error:\n", "            logger.warning(f\"Invalid product data: {error}\")\n", "            continue\n", "        valid_products.append(product)\n", "    \n", "    return valid_products\n", "```\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["A class like `ProductValidator` is important for ensuring data quality and consistency in our web scraping pipeline. It validates product data against required fields and format specifications before storage. This validation step helps prevent corrupted or incomplete data from entering our system, making downstream processing more reliable. The class provides static methods to validate individual products and entire product lists, checking for required fields like name and description, proper URL formatting, and valid upvote counts."]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Implement efficient storage"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "```python\n", "import json\n", "import gzip\n", "from pathlib import Path\n", "\n", "class DataManager:\n", "    def __init__(self, base_dir: str = \"data\"):\n", "        self.base_dir = Path(base_dir)\n", "        self.base_dir.mkdir(exist_ok=True)\n", "\n", "    def save_products(self, products: list, compress: bool = True):\n", "        \"\"\"Save products with optional compression.\"\"\"\n", "        date_str = datetime.now().strftime(\"%Y_%m_%d\")\n", "        \n", "        if compress:\n", "            filename = self.base_dir / f\"ph_products_{date_str}.json.gz\"\n", "            with gzip.open(filename, 'wt', encoding='utf-8') as f:\n", "                json.dump(products, f)\n", "        else:\n", "            filename = self.base_dir / f\"ph_products_{date_str}.json\"\n", "            with open(filename, 'w', encoding='utf-8') as f:\n", "                json.dump(products, f)\n", "\n", "    def load_products(self, date_str: str) -> list:\n", "        \"\"\"Load products for a specific date.\"\"\"\n", "        gz_file = self.base_dir / f\"ph_products_{date_str}.json.gz\"\n", "        json_file = self.base_dir / f\"ph_products_{date_str}.json\"\n", "        \n", "        if gz_file.exists():\n", "            with gzip.open(gz_file, 'rt', encoding='utf-8') as f:\n", "                return json.load(f)\n", "        elif json_file.exists():\n", "            with open(json_file, 'r', encoding='utf-8') as f:\n", "                return json.load(f)\n", "        return []\n", "```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The `DataManager` class extends our plain storage function from the previous sections. It provides a robust and organized way to handle data persistence for our web scraper. The class implements both compressed and uncompressed storage options using `gzip`, which helps optimize disk space usage while maintaining data accessibility. By organizing data by date and providing consistent file naming conventions, it enables easy tracking and retrieval of historical product data."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And that's a wrap! We've covered several ways to deploy web scrapers in 2025, from simple GitHub Actions to more complex setups with Heroku and PythonAnywhere. Each method has its own sweet spot:\n", "\n", "- GitHub Actions: Great for simple scrapers that run daily/weekly\n", "- Heroku: Perfect for more frequent scraping with its flexible scheduler\n", "- PythonAnywhere: Solid choice for beginners with its user-friendly interface\n", "\n", "Remember, start small and scale up as needed. No need to jump straight into complex setups if GitHub Actions does the job. The best deployment method is the one that matches your specific needs and technical comfort level. 🕷️\n", "\n", "Here are some related resources that might interest you:\n", "- [GitHub Actions documentation](https://docs.github.com/en/actions)\n", "- [Firecrawl documentation](docs.firecrawl.dev)\n", "- [Comprehensive guide on Firecrawl's `scrape_url` method](https://www.firecrawl.dev/blog/mastering-firecrawl-scrape-endpoint)\n", "- [How to generate sitemaps in Python using Firecrawl](https://www.firecrawl.dev/blog/how-to-generate-sitemaps-using-firecrawl-map-endpoint)\n", "\n", "Thank you for reading!"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.20"}}, "nbformat": 4, "nbformat_minor": 2}